Puesto: Junior Developer

Seniority: Junior

Level: 1, 2 y 3

Responsabilidades:
• Implementar features simples y bien definidas con supervisión
• Corregir bugs de complejidad baja a media
• Escribir pruebas unitarias para su código
• Participar en code reviews para aprender
• Documentar su trabajo de manera clara
• Comunicar bloqueos y problemas de manera oportuna
• Interpreta métricas básicas de código y rendimiento. Aprende a usar herramientas de monitoreo y logging.
• Adaptarse a cambios en requerimientos o tecnologías con apoyo del equipo
• Desarrollar con enfoque en usabilidad y accesibilidad, asegurando que las implementaciones no afecten negativamente la experiencia del usuario

Funciones Principales:
Innovación
• Hace preguntas sobre procesos y tecnologías para entender cómo se pueden mejorar.
• Propone pequeñas optimizaciones en código o flujos de trabajo con apoyo del equipo.
• Explora nuevas herramientas y comparte hallazgos con el equipo.

Alineación con los OKRs de la empresa
• Contribuye a la entrega de funcionalidades alineadas con los objetivos del producto.
• Mejora la calidad del código y reduce el número de bugs en producción.
• Apoya en la implementación de métricas básicas para el monitoreo del rendimiento.

Ejemplos de Comportamientos:
• Ejemplo positivo: Pide clarificación cuando las tareas no están claras en lugar de asumir.
• Ejemplo positivo: Documenta soluciones a problemas para referencia futura.
• Ejemplo positivo: Muestra disposición para aprender nuevas tecnologías cuando el equipo las introduce.
• Ejemplo positivo: Considera el impacto en el usuario final al desarrollar una funcionalidad.
• Área de desarrollo: Necesita supervisión frecuente para completar tareas.

Skills técnicas mínimas esperadas:
DevSecOps y Entrega Continua
• Git básico (commits, branches simples, pull requests)
• Testing unitario básico (Jest u otra herramienta similar)
• Uso de contenedores: Docker básico (build/run)
• Deploy
• Introducción a CI/CD (ejecución de pipelines ya configurados)Conocimiento básico de logs y errores de ejecución

Diseño y Arquitectura de Sistemas
• Comprensión de componentes básicos de sistemas
• Aplicación de patrones de diseño básicos
• Queries CRUD básicas en bases de datosNociones iniciales sobre diseño de APIs RESTful

Seguridad, Gobernanza y Gestión de Datos
• Aplicación de prácticas básicas de desarrollo seguro (validaciones, autenticación)
• Uso seguro de secretos y configuraciones (Secret Manager básico)
• Conciencia de buenas prácticas de datos (principios básicos de GDPR, HIPAA)Introducción a riesgos de seguridad en aplicaciones web

Innovación y Estrategia Técnica
• Participación en propuestas de pequeñas mejoras técnicas
• Uso de IA como asistencia en tareas operativas (ChatGPT, Claude, Cursor)Adopción de nuevas herramientas o frameworks básicos

Formación académica adicional:
En curso o egresado reciente de tecnicatura, bootcamp o carrera universitaria (Sistemas, Informática, etc.)

Requisitos de experiencia: Experiencia típica:0-2 años

Soft Skills requeridas:
Colaboración & Comunicación
Comprensión de las necesidades del cliente
Adaptabilidad & Agilidad
Capacidad Analítica - Data-Centric Thinking
Empatía
Innovación
Pensamiento Crítico
Autonomía e Impacto


Comportamientos esperados por cada soft skills de acuerdo al Seniority:
1. Comunicación y Colaboración
- Comparte información clara y oportuna.
- Colabora cuando se le solicita.
- Básica, principalmente escucha y aprende a comunicar problemas técnicos de manera clara

2. Autonomía e Impacto
- Cumple tareas con supervisión.
- Aprende a priorizar.
- Aporta valor en su rol.
- Completa tareas asignadas con supervisión alta o moderada.

3. Adaptabilidad y Agilidad
- Se adapta a cambios con guía.
- Acepta feedback y ajusta su accionar.
- Aprende de experiencias nuevas.
- Se adapta a herramientas y metodologías del equipo

4. Capacidad analítica - Data Centric
- Interpreta datos básicos con guía.
- Utiliza herramientas para obtener información relevante.
- Análisis de problemas concretos y acotados

5. Comprensión de la necesidad del Cliente/Usuario
- Comprende requerimientos básicos.
- Escucha activamente.
- Cumple con lo solicitado.
- Inicial, enfocada en entender requerimientos ya documentados.

6. Empatía
- Escucha con atención.
- Es respetuoso y receptivo a emociones ajenas.
- Muestra disposición para entender perspectivas de compañeros

7. Innovación
- Participa en iniciativas.
- Aporta ideas frescas.
- Se muestra abierto al cambio.
- Propone pequeñas mejoras en código o procesos que conoce bien

8. Pensamiento Crítico
- Cuestiona con respeto.
- Identifica errores básicos.
- Solicita guía para resolver problemas.
- Comienza a cuestionar soluciones y buscar alternativas.

Proximo Nivel: Semi Senior Developer



