# Pre-Evaluaciones de Performance Q2 2025

## Información General
- **<PERSON><PERSON><PERSON> Principal**: <PERSON><PERSON><PERSON> (Tech Lead)
- **Periodo <PERSON>ado**: Q2 2025 (Abril - Junio 2025)
- **Fecha de Pre-Evaluación**: Julio 2025

---

## **📝 PRE-EVALUACIÓN: <PERSON>**

**Rol del Evaluado:** Analista SR (Backend Developer Sr)  
**Periodo Evaluado:** Q2 2025

### **💡 COMPETENCIAS**

| Competencia | Nivel (1-4) | Comentarios del Evaluador Principal |
| :---- | :---- | :---- |
| Comprensión de la necesidad del Cliente | 4 | Demostró comprensión excepcional en arquitecturas propuestas para Synapse. Capacidad de traducir necesidades de negocio a soluciones técnicas. |
| Empatía | 4 | Excelente mentoría con Cristian. Reconocimiento transversal del equipo por su impacto y apoyo. |
| Adaptabilidad y Agilidad | 4 | Adaptación excepcional a liderazgo técnico. Transición exitosa de Semi Senior a Senior en Q2. |
| Capacidad analítica - Data Centric | 4 | Diagramas de arquitectura de alta calidad. Propuestas técnicas implementadas por el equipo. |
| Autonomía e Impacto | 4 | "Una vez orientado, toma el camino y sigue". Impacto transversal reconocido por múltiples equipos. |
| Colaboración y Comunicación | 4 | Liderazgo natural demostrado. Facilitación efectiva de reuniones técnicas. |
| Innovación | 3 | Propuestas arquitecturales innovadoras, aunque con enfoque más en optimización que disrupción. |
| Pensamiento Crítico | 4 | Capacidad excepcional para identificar problemas y proponer soluciones. Honestidad técnica valorada. |
| Liderazgo y Mentoring | 4 | Mentoría exitosa con Cristian. Reconocido como referente técnico por el equipo. |
| Habilidad Técnica | 4 | Nivel técnico excepcional. Capacitación en System Design completada con excelencia. |

### **💬 Feedback del Evaluador Principal**

**1. ¿Qué cualidades o fortalezas destacás de su desempeño durante el semestre?**  
Tobias demostró un crecimiento excepcional durante Q2, evolucionando de "Senior Minus" a un referente técnico consolidado. Su capacidad de liderazgo natural se manifestó en la mentoría exitosa con Cristian y el reconocimiento transversal del equipo. Destacan su autonomía ("shadowing de mi rol sin tener la responsabilidad"), su capacidad arquitectural (diagramas implementados por el equipo) y su madurez profesional. Completó todos sus objetivos Q2 al 100%.

**2. ¿En qué aspectos puede seguir desarrollándose?**  
Continuar desarrollando habilidades de gestión de equipos más grandes. Balancear su tendencia al análisis profundo con la necesidad de acción rápida en algunos contextos. Explorar más oportunidades de innovación disruptiva más allá de optimización de procesos existentes.

**3. ¿Qué nivel de impacto tuvo su desempeño?**  
Impacto excepcional. Se convirtió en el líder técnico de facto del equipo Synapse, permitiendo que el Tech Lead se enfoque en gestión. Su mentoría elevó el nivel de Cristian significativamente. Sus arquitecturas fueron adoptadas por múltiples equipos. Reconocido como "referente técnico" por equipos indirectos.

---

## **📝 PRE-EVALUACIÓN: Nicolás Díaz**

**Rol del Evaluado:** Staff Engineer (Especialista Security En Proceso)  
**Periodo Evaluado:** Q2 2025

### **💡 COMPETENCIAS**

| Competencia | Nivel (1-4) | Comentarios del Evaluador Principal |
| :---- | :---- | :---- |
| Comprensión de la necesidad del Cliente | 3 | Buen entendimiento del negocio. Enfoque en calidad y seguridad alineado con necesidades organizacionales. |
| Empatía | 3 | Comunicación transparente sobre situaciones complejas del equipo. Colaboración efectiva. |
| Adaptabilidad y Agilidad | 3 | Transición exitosa de Tech Lead a IC. Adaptación a especialización en seguridad. |
| Capacidad analítica - Data Centric | 3 | Implementación efectiva de procesos de seguridad en SDLC. Análisis técnico sólido. |
| Autonomía e Impacto | 4 | Excelente autonomía. Compensación proactiva de tiempo perdido. Ownership completo de iniciativas. |
| Colaboración y Comunicación | 3 | Comunicación honesta y transparente. Buen nivel de colaboración técnica. |
| Innovación | 3 | Implementación de prácticas de seguridad innovadoras para la organización. |
| Pensamiento Crítico | 3 | Identificación de necesidades de mejora en calidad y seguridad. |
| Liderazgo y Mentoring | 3 | Liderazgo técnico en iniciativas de seguridad. Apoyo a equipos en mejores prácticas. |
| Habilidad Técnica | 4 | Nivel técnico sólido. Progreso excelente en especialización CSSLP. |

### **💬 Feedback del Evaluador Principal**

**1. ¿Qué cualidades o fortalezas destacás de su desempeño?**  
Nicolás demostró excelente responsabilidad y autonomía, compensando proactivamente tiempo perdido por kinesiología. Su especialización en seguridad progresó significativamente con la capacitación CSSLP. Implementó procesos de seguridad en SDLC de manera efectiva. Su transparencia y comunicación honesta sobre situaciones del equipo son valiosas para la gestión.

**2. ¿En qué aspectos puede seguir desarrollándose?**  
Continuar profundizando en especialización de seguridad. Desarrollar más habilidades de evangelización técnica para expandir impacto. Explorar oportunidades de liderazgo técnico más amplias dentro de su track de IC.

**3. ¿Qué nivel de impacto tuvo su desempeño?**  
Impacto sólido en calidad y seguridad organizacional. Sus tests y documentación benefician a todo el equipo. La implementación de prácticas de seguridad en SDLC establece bases para mejoras futuras. Su especialización en seguridad posiciona a la organización mejor para desafíos futuros.

---

## **📝 PRE-EVALUACIÓN: Micaela Percovich**

**Rol del Evaluado:** Analista SSR (Backend Developer Ssr)  
**Periodo Evaluado:** Q2 2025

### **💡 COMPETENCIAS**

| Competencia | Nivel (1-4) | Comentarios del Evaluador Principal |
| :---- | :---- | :---- |
| Comprensión de la necesidad del Cliente | 4 | Excelente comprensión de necesidades DevOps organizacionales. Capacitaciones inter-equipos muy efectivas. |
| Empatía | 4 | Rol de "traductora" entre equipos y DevOps. Facilitación excepcional de comunicación. |
| Adaptabilidad y Agilidad | 4 | Adaptación excepcional a múltiples roles: desarrollo, DevOps, facilitación. |
| Capacidad analítica - Data Centric | 3 | Análisis efectivo de necesidades técnicas. Enfoque práctico en soluciones. |
| Autonomía e Impacto | 4 | "El perfil que más creció del equipo". Autonomía excepcional una vez orientada. |
| Colaboración y Comunicación | 4 | Sinergia excelente con Javier. Impacto organizacional en otros equipos. |
| Innovación | 3 | Iniciativas innovadoras en capacitación DevOps. Enfoque práctico y efectivo. |
| Pensamiento Crítico | 3 | Capacidad de rebatir y debatir decisiones técnicas con criterio. |
| Liderazgo y Mentoring | 4 | Evolución hacia ownership y certeza técnica. Liderazgo natural emergente. |
| Habilidad Técnica | 4 | Perfil híbrido desarrollo + DevOps muy valioso. Progreso excelente en certificación GCP. |

### **💬 Feedback del Evaluador Principal**

**1. ¿Qué cualidades o fortalezas destacás de su desempeño?**  
Micaela fue reconocida como "el perfil que más creció del equipo" durante Q2. Su evolución hacia liderazgo natural, combinada con su perfil híbrido desarrollo + DevOps, la convierte en un activo excepcional. Su rol como "traductora" entre equipos y DevOps ha sido estratégico. Demostró ownership, certeza técnica y capacidad de facilitación madura. Su sinergia con Javier ha sido ejemplar.

**2. ¿En qué aspectos puede seguir desarrollándose?**  
Continuar desarrollando habilidades de liderazgo formal. Profundizar en certificación GCP DevOps. Explorar oportunidades de mentoría técnica con otros miembros del equipo.

**3. ¿Qué nivel de impacto tuvo su desempeño?**  
Impacto organizacional excepcional. Sus capacitaciones DevOps elevaron la autonomía de múltiples equipos. Su colaboración con Javier distribuyó efectivamente conocimiento crítico. Su crecimiento técnico y de liderazgo posiciona al equipo para mayor escalabilidad.

---

## **📝 PRE-EVALUACIÓN: Joel Camatta**

**Rol del Evaluado:** Analista SSR (Backend Developer Ssr)  
**Periodo Evaluado:** Q2 2025

### **💡 COMPETENCIAS**

| Competencia | Nivel (1-4) | Comentarios del Evaluador Principal |
| :---- | :---- | :---- |
| Comprensión de la necesidad del Cliente | 3 | Buen entendimiento técnico. Experiencia valiosa en proyecto RIMAC. |
| Empatía | 3 | Colaboración efectiva con equipos. Frustración comprensible por programa agentes. |
| Adaptabilidad y Agilidad | 3 | Adaptación a múltiples proyectos. Desafíos en liderazgo de equipos grandes. |
| Capacidad analítica - Data Centric | 4 | Capacidad excepcional de resolución técnica sin experiencia previa. |
| Autonomía e Impacto | 3 | Buen ownership técnico. Desafíos en gestión de recursos humanos. |
| Colaboración y Comunicación | 3 | Liderazgo técnico efectivo en reuniones. Dificultades con equipos no comprometidos. |
| Innovación | 3 | Interés en IA y tecnologías emergentes. Enfoque en arquitectura empresarial. |
| Pensamiento Crítico | 4 | Análisis técnico excepcional. Identificación clara de problemas organizacionales. |
| Liderazgo y Mentoring | 2 | Experiencia negativa liderando programa agentes. Necesita desarrollo en gestión de equipos. |
| Habilidad Técnica | 4 | "Nivel muy bueno, eres un referente en UMA". Capacidad técnica excepcional. |

### **💬 Feedback del Evaluador Principal**

**1. ¿Qué cualidades o fortalezas destacás de su desempeño?**  
Joel demostró nivel técnico excepcional, siendo reconocido como "referente en UMA". Su capacidad de resolver problemas técnicos sin experiencia previa es notable. Lideró efectivamente conversaciones técnicas entre equipos RIMAC-Synapse. Su participación en capacitación System Design fue excelente. Merece promoción a Senior por competencia técnica.

**2. ¿En qué aspectos puede seguir desarrollándose?**  
Desarrollar habilidades de gestión de equipos y liderazgo de personas. Aprender técnicas de motivación y engagement de equipos. Balancear expectativas técnicas con realidades de gestión humana. Explorar certificaciones en arquitectura empresarial (TOGAF).

**3. ¿Qué nivel de impacto tuvo su desempeño?**  
Impacto técnico excepcional en proyectos individuales. Contribución valiosa en coordinación técnica entre equipos. La experiencia negativa con programa agentes proporcionó aprendizajes importantes sobre liderazgo. Su nivel técnico lo posiciona como futuro arquitecto del equipo.

---

## **📝 PRE-EVALUACIÓN: Cristian Murua**

**Rol del Evaluado:** Analista JR (Backend Developer Jr)  
**Periodo Evaluado:** Q2 2025

### **💡 COMPETENCIAS**

| Competencia | Nivel (1-4) | Comentarios del Evaluador Principal |
| :---- | :---- | :---- |
| Comprensión de la necesidad del Cliente | 3 | Mejora notable en comprensión de requerimientos. Curso FHIR completado exitosamente. |
| Empatía | 3 | Buena colaboración con equipo. Receptivo a feedback y mentoría. |
| Adaptabilidad y Agilidad | 3 | Adaptación exitosa a Synapse. Superación de crisis vocacional. |
| Capacidad analítica - Data Centric | 2 | En desarrollo. Progreso notable con mentoría de Tobias. |
| Autonomía e Impacto | 2 | "Ya no requieres tanto acompañamiento". Crecimiento en autonomía. |
| Colaboración y Comunicación | 3 | "Tienes muy buen enganche con la gente". Colaboración efectiva. |
| Innovación | 2 | Interés emergente en IA. Enfoque en aprendizaje fundamental. |
| Pensamiento Crítico | 2 | En desarrollo. Mejora en análisis técnico con apoyo. |
| Liderazgo y Mentoring | 1 | No aplica para nivel Junior. Enfoque en desarrollo personal. |
| Habilidad Técnica | 3 | "Como Junior sobrecumples en la mayoría de áreas". Progreso notable. |

### **💬 Feedback del Evaluador Principal**

**1. ¿Qué cualidades o fortalezas destacás de su desempeño?**  
Cristian demostró crecimiento excepcional: "Has crecido una enormidad en estos tres meses". Superó crisis vocacional y confirmó continuidad en UMA. Como Junior sobrecumple en mayoría de áreas. Completó curso FHIR exitosamente. Su colaboración con el equipo es excelente. Muestra "trazas para ser Semi Senior consolidado".

**2. ¿En qué aspectos puede seguir desarrollándose?**  
Continuar trabajando en gestión de autoexigencia. Desarrollar mayor participación en canales técnicos. Fortalecer autonomía técnica. Completar transición de mentoría con Tobias y establecer nuevo mentor.

**3. ¿Qué nivel de impacto tuvo su desempeño?**  
Impacto positivo en estabilidad del equipo al confirmar continuidad. Su crecimiento técnico contribuye a capacidad del equipo. La superación de su crisis personal fortalece la cohesión del equipo. Progreso hacia Semi Senior para fin de año es realista.

---

## **📝 PRE-EVALUACIÓN: Javier Sankowicz**

**Rol del Evaluado:** Analista SSR (DevOps Ssr)  
**Periodo Evaluado:** Q2 2025

### **💡 COMPETENCIAS**

| Competencia | Nivel (1-4) | Comentarios del Evaluador Principal |
| :---- | :---- | :---- |
| Comprensión de la necesidad del Cliente | 4 | Excelente comprensión de necesidades organizacionales. Documento Aeros "muy propositivo". |
| Empatía | 3 | Colaboración efectiva con Micaela. Apoyo técnico oportuno a equipos. |
| Adaptabilidad y Agilidad | 3 | Adaptación a múltiples crisis simultáneas. Gestión efectiva de prioridades. |
| Capacidad analítica - Data Centric | 4 | Análisis técnico excepcional. Identificación de gastos hormiga y optimizaciones. |
| Autonomía e Impacto | 4 | "Eres un referente en estas cosas". Liderazgo técnico consolidado. |
| Colaboración y Comunicación | 3 | Conducción efectiva de reuniones técnicas. Facilitación entre equipos. |
| Innovación | 3 | Propuestas constructivas para mejoras de proceso. Visión estratégica. |
| Pensamiento Crítico | 4 | Identificación clara de problemas sistémicos. Propuestas de solución estructuradas. |
| Liderazgo y Mentoring | 3 | Mentoría técnica efectiva. Necesita ampliar equipo para mayor impacto. |
| Habilidad Técnica | 4 | Expertise consolidado en DevOps/Cloud. Progreso hacia certificación CKA. |

### **💬 Feedback del Evaluador Principal**

**1. ¿Qué cualidades o fortalezas destacás de su desempeño?**  
Javier completó objetivos Q2 exitosamente: separación de repos, pipeline CI/CD, mejoras de seguridad. Su documento para Aeros fue "excelente y propositivo". Demostró liderazgo técnico consolidado y visión estratégica. Su experiencia como único DevOps por 5 años lo convierte en referente organizacional. Identificó optimizaciones de costos significativas.

**2. ¿En qué aspectos puede seguir desarrollándose?**  
Desarrollar equipo DevOps para distribuir responsabilidades. Continuar con certificación CKA. Explorar proyectos de arquitectura cloud más desafiantes. Implementar estrategia de monitoreo centralizado.

**3. ¿Qué nivel de impacto tuvo su desempeño?**  
Impacto crítico en infraestructura organizacional. Sus implementaciones de seguridad y CI/CD benefician a todos los equipos. Su colaboración con Micaela distribuyó conocimiento DevOps efectivamente. Su experiencia y propuestas son fundamentales para escalabilidad organizacional.
