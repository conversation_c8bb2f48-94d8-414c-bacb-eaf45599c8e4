# 1:1 con <PERSON> - 29/05/2025

## Información básica
- **Miembro del equipo**: <PERSON>
- **<PERSON>cha**: 29/05/2025
- **Duración**: 36 minutos (12:30 - 13:06)
- **Ciclo de revisión**: Regular
- **<PERSON><PERSON> clave**: Vacaciones, Demo CI/CD, Coordinación, Seguridad, Liderazgo
- **Nivel de satisfacción**: 3 - Regular
- **Seguimiento requerido**: Sí
- **Relación con OKRs**: Objetivos Q2 - Pipeline CI/CD y coordinación técnica

## Seguimiento de acciones previas
- Salida gradual de proyecto Rimac (completado exitosamente)
- Desarrollo de pipeline CI/CD (en progreso avanzado)
- Coordinación con Mica para apoyo DevOps (funcionando)

## Temas tratados
- Planificación de vacaciones en Israel (15 junio - 27 junio)
- Progreso y demo del pipeline CI/CD con canary deployment
- Coordinación técnica con equipos de desarrollo
- Gestión de proyecto temporal de escriba con permisos de seguridad
- Falta de coordinación en proyectos y visibilidad de iniciativas
- Necesidad de estandarización en procesos de desarrollo
- Coordinación con tracks de arquitectura y seguridad

## Logros desde la última reunión
- Salida exitosa de proyecto Rimac según cronograma
- Avance significativo en pipeline CI/CD con Mica y Fernando
- Resolución de problema crítico de Policía Federal (reconocimiento recibido)
- Gestión responsable de permisos temporales para proyecto escriba
- Coordinación efectiva en mudanza y organización personal

## Logros no reportados
- Apoyo técnico oportuno en situaciones críticas
- Implementación de mejores prácticas de seguridad en proyectos temporales
- Mentoría técnica a desarrolladores en temas de infraestructura

## Bloqueos reportados
- Fernando sobrecargado, sin tiempo para testing en pipeline CI/CD
- Falta de coordinación entre equipos en iniciativas técnicas
- Ausencia de visibilidad sobre qué proyectos están desarrollando otros
- Necesidad de definir métricas y umbrales para canary deployment

## Desafíos actuales
- Completar demo de pipeline CI/CD antes de vacaciones
- Definir métricas de error rate para automatización de rollback
- Coordinar con Aeros para definición de umbrales de calidad
- Mejorar coordinación entre tracks técnicos (DevOps, Arquitectura, Security)
- Estandarizar procesos de desarrollo con definition of done

## Bienestar y equilibrio trabajo-vida
- **Estado personal**: Progreso positivo en mudanza y organización del hogar
- **Salud**: Bien, vacuna antigripal efectiva, sin problemas reportados
- **Planificación**: Viaje a Israel bien organizado (15-27 junio)
- **Cambio de aerolínea**: Adaptación necesaria por situación geopolítica
- **Proyectos personales**: Armado de muebles, instalación de tender colgante

## Feedback bidireccional
### Observaciones sobre Javier
- **Responsabilidad técnica**: Gestión cuidadosa de permisos de seguridad temporales
- **Disponibilidad**: Apoyo oportuno en situaciones críticas del equipo
- **Visión técnica**: Identificación de mejores prácticas y problemas de coordinación
- **Expertise**: Conocimiento profundo de infraestructura y DevOps
- **Proactividad**: Propuestas concretas para mejoras en procesos

### Feedback para el líder
- **Apoyo en salida de Rimac**: Valoración de gestión para liberarlo del proyecto
- **Flexibilidad**: Apreciación por adaptación a cambios de cronograma
- **Coordinación**: Reconocimiento de esfuerzos por mejorar visibilidad entre equipos

## Plan de Carrera (Observaciones / acciones)
- **Especialización DevOps**: Consolidación de expertise en CI/CD y automatización
- **Liderazgo técnico**: Rol de referente en infraestructura y mejores prácticas
- **Coordinación transversal**: Integración con tracks de arquitectura y seguridad
- **Mentoring**: Apoyo a desarrolladores en temas de infraestructura

## Métricas de crecimiento
| Habilidad/Área | Estado actual | Objetivo | Progreso |
|----------------|---------------|----------|----------|
| Pipeline CI/CD | Avanzado | Demo completa | 80% |
| Coordinación técnica | En desarrollo | Mejorar visibilidad | En progreso |
| Seguridad DevOps | Senior | Senior | Cumplido |
| Mentoring desarrolladores | En desarrollo | Estructurar | En progreso |

## Recursos de aprendizaje recomendados
- **Mesa de trabajo con Arquitectura**: Coordinación con Tobias y Joel
- **Reunión con Nicolás**: Alineación en temas de seguridad
- **Coordinación con Aeros**: Definición de métricas y umbrales
- **Estandarización de procesos**: Definition of done y definition of ready

## Alineación con valores de la empresa
- **Responsabilidad**: Gestión cuidadosa de permisos de seguridad
- **Colaboración**: Apoyo proactivo en situaciones críticas
- **Mejora continua**: Identificación y propuesta de mejoras en procesos
- **Expertise técnica**: Aplicación de mejores prácticas en infraestructura

## Objetivos para la próxima reunión
- Completar demo de pipeline CI/CD con métricas definidas
- Evaluar coordinación con tracks de arquitectura y seguridad
- Seguimiento de estandarización de procesos de desarrollo
- Planificación post-vacaciones y nuevas iniciativas

## Acuerdos y acciones
| Acción | Responsable | Fecha límite | Prioridad |
|--------|-------------|--------------|-----------|
| Aprobar solicitud de vacaciones (15-27 junio) | Mauricio | 30/05/2025 | Alta |
| Extender permisos proyecto escriba hasta 8 junio | Javier | 30/05/2025 | Alta |
| Agendar mesa de trabajo con Tobias y Joel (Arquitectura) | Mauricio | 05/06/2025 | Media |
| Agendar reunión con Nicolás (Seguridad) | Mauricio | 05/06/2025 | Media |
| Definir métricas con Aeros para canary deployment | Javier | 10/06/2025 | Media |
| Preparar demo completa de pipeline CI/CD | Javier/Mica | 14/06/2025 | Alta |
| Estandarizar definition of done con líderes | Mauricio | 30/06/2025 | Media |

## Notas adicionales
Esta reunión se centró en aspectos técnicos y de coordinación, mostrando a Javier en un estado más estable que reportes previos de otros miembros del equipo.

**Pipeline CI/CD**: Progreso significativo con Mica en el desarrollo del pipeline con canary deployment. El desafío principal es la definición de métricas de error rate y umbrales para automatización de rollback. Fernando está sobrecargado para apoyar en testing, lo que requiere coordinación con Farid para priorización o recursos adicionales.

**Proyecto Escriba**: Javier demostró responsabilidad técnica al implementar permisos temporales con expiración automática para un proyecto de IA. Reconoce los riesgos de seguridad y propone soluciones estructuradas para proyectos similares.

**Coordinación Técnica**: Identificó problemas importantes de visibilidad entre equipos. Ejemplos: no saber que Tobias ya había elegido herramientas para colas de mensajes, o desconocer el alcance del trabajo de Nicolás en seguridad. Esto llevó a acordar mesas de trabajo específicas para mejorar coordinación.

**Vacaciones en Israel**: Planificación bien organizada (15-27 junio) con adaptación necesaria por cambio de aerolínea debido a situación geopolítica. Sin impacto en proyectos críticos.

**Estandarización de Procesos**: Propuesta valiosa sobre definition of done y definition of ready para incluir criterios de seguridad desde el inicio del desarrollo, no como agregado posterior.

La reunión mostró a Javier enfocado en aspectos técnicos y mejoras de proceso, sin manifestar las frustraciones reportadas por otros miembros del equipo. Su enfoque en coordinación y mejores prácticas demuestra madurez técnica y visión organizacional.
