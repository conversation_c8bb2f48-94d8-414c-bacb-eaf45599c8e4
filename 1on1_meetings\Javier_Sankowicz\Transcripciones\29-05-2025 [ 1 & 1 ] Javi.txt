# [ 1 & 1 ] <PERSON><PERSON> / <PERSON><PERSON><PERSON>

  Meeting started: 29/5/2025, 12:30:22
  Duration: 36 minutes
  Participants: <PERSON>, <PERSON><PERSON><PERSON>

  ## Transcript

  00:00 <PERSON>: Hola
00:01 <PERSON><PERSON><PERSON>: Hola Javi cómo estás
00:03 <PERSON>: Buen día.
00:05 <PERSON><PERSON><PERSON>: Cómo va todo?
00:07 <PERSON>: Bien.
00:08 <PERSON><PERSON><PERSON>: Todo bien, disculpa las múltiples movidas Pero como farid se va de vacaciones. Las las reuniones han aparecido ahí subrepticiamente y sean alargados.
00:23 <PERSON>: <PERSON><PERSON> bueno, él vuelve y después me voy yo así que
00:25 <PERSON><PERSON><PERSON>: Así es así es Así vi pasar en la solicitud de lo que me habías comentado En todo caso. Ya tienes planificado el viaje ya?
00:33 <PERSON>: Cómo más o menos ayer yo tenía Como idea, o sea viajar viejo el 15. Tenía como idea volver el tipo 3 de Julio algo así. Por temas de aerolíneas que bueno iberia no se anima a viajar, hubo que hacer un cambio de aerolínea. Y me decían Bueno okay, podrías Volver El Seis de julio? Cambiaron a emirates y es como Bueno hay diferencia por el cambio de pasaje.
01:12 Javier Sankowicz: Y Ya apareció un montón y dije Bueno Ok me vuelvo cuando termina el programa el 25. de junio Entonces nada dos semanas Una lástima Pues podría haberme quedado una semana más.
01:30 Mauricio Gomez Quijada: Sí pero
01:33 Javier Sankowicz: De plata gastos y demás yo estaba yendo para un el casamiento de una amiga, el 25 le dije eché Mirá me voy a perder tu casamiento. Te veo unos días antes.
01:43 Mauricio Gomez Quijada: Más encima que terrible esto de las aerolíneas siempre echan a perder todos los planes.
01:49 Javier Sankowicz: Etiopian está viajando Emilia se está viajando iberia Bueno qué sé yo Paisa país con gobierno antisemita no se anima a viajar.
01:58 Mauricio Gomez Quijada: Pero bueno hay que adaptarse a veces a las situaciones. Pero bueno esta eso está en una solicitud de aquí sigue allá de junio al 27 de junio que
02:17 Javier Sankowicz: Sí, me pido esas dos semanas.
02:21 Mauricio Gomez Quijada: Perfecto excelente no hay problema hay en un ratito. Apruebo la lo que me corresponde a mí. Así que no no hay problema Cómo has estado Javi cómo has estado la salud el ánimo la casa
02:36 Javier Sankowicz: Avanzando tipo la vez pasada la que fue juez de La semana pasada me llegó, me llegaron dos muebles para armar. no quería pagar bastante extra porque me lo arden y Bueno aparte es como jugar con LEGO El fin estuve Armando uno le faltaban unas piecitas, digamos unas cantoneras y una cantonera y no sé si algo más saqué de las piezas del otro mueble y después tipo así terminaba, me quejé tipo, me tuvieron que mandar otra bolsita con las piezas.
03:09 Javier Sankowicz: Así que este fin de armo el otro mueble Ah Tengo que hablar con mi tía para que me pase el contacto para Que eran el lavadero poner un tender colgante de techo. podría ponerlos de pared, así no más que se abren como tijera pero Quiero ver presupuesto por uno de techo así quizás que es un poco más cómodo digamos me Dan una sensación de que el otro se me va a romper más fácil porque a ver, está colgado de cosas en la pared y tiene peso así como que el equilibrio de
03:40 Mauricio Gomez Quijada: claro
03:41 Javier Sankowicz: peso.
03:41 Mauricio Gomez Quijada: El torque que se genera ahí.
03:44 Javier Sankowicz: Sí entonces Quiero ver eso Y quizá pedir presupuesto si no es tan caro lo hago Ya fue. Así no tengo que andar para cualquier cosita con un tender en el balcón. No sé de la cama, ya está, estuve comprando sábanas y esas cosas, mi tía me quiso arreglar algo de acolchado. Yo no quiero que gaste porque bueno, se jubiló hace poco. Ah qué sé yo bien de salud bien, no sé Supongo que la vacuna antigripal funciona yo no me
04:17 Mauricio Gomez Quijada: gripe allá en Argentina
04:19 Javier Sankowicz: No sé, tengo alguna que otro amigo que se haya se enfermó y demás yo fui tipo ahí en emergencia, me di la vacuna.
04:26 Mauricio Gomez Quijada: Ya ninguna reacción contraria a la vacuna ni
04:29 Javier Sankowicz: No cero Supongo que era médico la que o enfermera o algo la que me puso me aplicó ella dijo que no eso era en otra vez hace varios años. Ahora ya no te da efectos adversos.
04:39 Mauricio Gomez Quijada: Nada genial.
04:41 Javier Sankowicz: Bueno, tuvo razón, no me dio nada.
04:43 Mauricio Gomez Quijada: es positivo es positivo Así que hay que esperar que pase que pase la época complicada de la gripe para saber si fue efectiva así que
04:54 Javier Sankowicz: hoy al fin parece que pudieron solucionarlo de Policía Federal
05:04 Mauricio Gomez Quijada: Sí, lo estaban ahí bastante complicados No yo no entendí mucho, estuve como colateral.
05:10 Javier Sankowicz: tuvieron un hackeo un ransomware entonces estuvieron metiendo la cosa de Security se ve que bloquearon todas las recogidas en la Argentina Y les pasamos y pez nuestras. Bueno, seguía bloqueando Porque algo metieron mal en una regla cambiaron algo y bueno. Yo lo único que hice fue abrir una vez una máquina virtual y hacer ejecutar el CURP que me dijeron y mostrarle que la máquina que esté salida de la máquina era la que le dije
05:40 Mauricio Gomez Quijada: Bueno pero esas son las idas y vueltas Bueno y también el Ah sí, si vi un reconocimiento fue por eso.
05:53 Javier Sankowicz: Yo le dije no me vas a poder un reconocimiento por tirar un cura en una consola.
05:57 Mauricio Gomez Quijada: Bueno pero a veces cuando es el momento crítico esa mano es la que salva la situación, así que
06:05 Javier Sankowicz: Momento crítico hace dos meses que lo que mandaron ese código y nunca lo testearon no hay, no se puede desorar de momento crítico.
06:12 Mauricio Gomez Quijada: Bueno pero momento crítico para el equipo porque a lo mejor estaban ya con el deadline encima y a pesar de todo lo que sea los trabajos o lo que sea
06:23 Mauricio Gomez Quijada: lo necesitaban ahora por su planificación lo que sea y tú tuviste la disponibilidad lo solucionaste y Y lo hiciste sentir bien Así que está bien. Es entendible podemos subir la vara, pero también podemos. Tomar en cuenta el hecho y para mí, gracias por por haber apoyado el equipo.
06:43 Javier Sankowicz: Bueno, hablando de subir la vara tipo, el proyecto de escriba que iba a ser temporal para el congreso ese. Yo le puse, pero claramente le puse permisos temporales Romi desde Lo legal, me avaló. Por la parte de seguridad y bueno los permisos expiraban ayer se ve que lo quieren seguir usando Bueno lo extendí hasta que me voy de vacaciones.
07:10 Mauricio Gomez Quijada: en realidad ahí hay que
07:11 Javier Sankowicz: Porque está abierto a lo que quieras y es un agujero de seguridad, entonces las dos semanas que yo no estoy, se le va a cortar sin sin No si lo necesitan se le va a cortar en el medio.
07:23 Mauricio Gomez Quijada: No yo te propondría otra cosa, yo te propondría que lo extendieras una semana, o sea, la próxima semana y la próxima semana hay que avisarle le notifico yo
07:34 Mauricio Gomez Quijada: quien quiera. Que tienen que hacer el proyecto para que se tome bien la magnitud de lo que se le va a hacer y luego se va a cortar esto por un tema de seguridad y ahí Romi nos va a avalar en tema de compliance Así que no hay problema.
07:47 Javier Sankowicz: como quiera yo por ahora puse hasta el Domingo 15 de junio si querés que lo extienda Solamente hasta antes no tengo problema.
07:57 Mauricio Gomez Quijada: Sí, extiende cámbialo, déjalo hasta a ver a cuánto estamos dejando la fecha. Estaba 27 extiéndelo hasta el domingo ocho de mayo.
08:10 Javier Sankowicz: para una semana menos a la hora, después lo
08:12 Mauricio Gomez Quijada: Perdón ocho de junio.
08:13 Javier Sankowicz: Ahora después lo cambio.
08:15 Mauricio Gomez Quijada: Sí, ocho de junio y esta semana este día y la próxima semana que vean que van
08:20 Mauricio Gomez Quijada: a hacer con ese proyecto, pero no lo podemos dejar. Abierto así me lo anoto.
08:27 Javier Sankowicz: Bien.
08:29 Mauricio Gomez Quijada: Está el proyecto de escriba esto lo ve. Inteligencia artificial cierto dato
08:36 Javier Sankowicz: Sí, está en el proyecto de Inteligencia artificial, están usando con el doc Y sí, no sé. El doc me dijo, creo que lo iban a presentar en lo de litua, que no me acuerdo cuando es.
08:51 Mauricio Gomez Quijada: Sí no hay problema, déjalo ahí yo lo dejo pasado y después lo pinpone con ellos, no para que decidamos por decir va a quedar Definitivamente hay que cerrarle los permisos, hay que hacer un proyecto con los permisos cerrados cambiarle los permisos.
09:05 Javier Sankowicz: Sí o sea armarlo bien, porque encima está el código, está en un repositorio Público de yanning, El que no sé si utilizan ese. frente, sí es como
09:18 Mauricio Gomez Quijada: Sí, no está bien.
09:19 Javier Sankowicz: O sea, era temporal y se hizo de forma temporal nada. Pero me me gustó esto de usarlos conditional para hacer permisos temporales para este tipo de proyecto que después quedan helada consumen recursos.
09:34 Mauricio Gomez Quijada: Sí, no es preferible eso y nos demoramos un poquito más y lo dejamos y si es que ellos deciden de que hay que a no ser que insisto alguien, venga y diga no es para un congreso que va a ser el 20 de junio. Bueno se lo dejamos hasta el 25 y hay una solicitud formal y se asumen las responsabilidades porque o si no después empieza no sé si aquí pero en todos los lados donde yo estaba después Ah pero es que yo de haber sabido que esto era tan peligroso te hubiera dicho que no, si no era tan urgente y terminan todas las responsabilidades en uno y eso a mí no me gusta.
10:09 Mauricio Gomez Quijada: Entonces prefiero que alguien diga y se responsabilice y nosotros no, no ponemos ningún problema, creo yo. Así que eso me parece bien, gracias por comentarmelo. Aquí tú me escribiste algunas cositas. Creo que sí, cómo vamos para la para la demo a la tarde Hay una demo.
10:32 Javier Sankowicz: A ver, hace dos semanas Tuvimos una mico Federico mica creo que bastante hubieras podido sumar que la idea de samid era decir bueno.
10:39 Mauricio Gomez Quijada: No sí
10:42 Javier Sankowicz: lo que te lo que yo ya tenía armado del pipeline en claudiploy, pero Qué Qué cambios? Deberíamos hacer y dijimos, bueno, pusimos ahí en el en la descripción de la mit tenemos que hacer estas cosas. Yo me llevé a lo que fue una. De momento después que terminamos la mica ayer estuvo laburando con la parte de Jess que creo ya lo dejó para la simular esas cosas. Había algo del que iba a revisar ella del Quality de sonar para ese.
11:18 Javier Sankowicz: Proyecto de fantasía que no sé si lo puedo hacer ahora le consulto y bueno Fernando tuvo tiempo hacer nada. Con la parte de para ver qué hacemos para lo del verify en cuanto a testing no digo, se ejecuta tal cosa de él me dijo que se llama antes para este caso de vacantes de servicio no es en duen porque no hay.
11:41 Mauricio Gomez Quijada: Me parece, me parece bueno, bueno, el plan de también de consultarlo con con
11:41 Javier Sankowicz: o sea, es un solo servicio, si fuese frontera dice Son tipo si son en Pero bueno no, no tuvo tiempo tipo estaba está sobrecargado. Y yo lo que estaba empezando a ver hoy y estoy no estoy viendo, estoy consultando con los chicos para ver Bueno si queremos hacerlo de las métricas tipo en el canal. Que es hasta se me ocurrió que bueno, yo puedo al contenedor, que se ejecuta en el verify para lo de las métricas, le puedo pasar una variable entorno que diga que Target si es un target de producción lo ejecuto si lo dejo pasar para que solo se ejecuten las fases del canari.
12:25 Javier Sankowicz: Es más, tendría que hacer hasta algo para que solo Incluso si el target es producción que solo se ejecute en el canal, Porque si desea si es un rolback automático por falla ahí no se tiene que ejecutar. eso se puede hacer por escrito y según un par de variables que te da Claudio Y después la parte que sí más bueno Okay cómo medimos el tema del arroz porque yo tipo tengo la tengo, puedo hacer las series temporales que me lo miden.
12:53 Javier Sankowicz: Errores, creo que lo miden no sé si en errores por segundo es rico es por segundo. Y no sé decir bueno Qué valores, tomaríamos Qué significa eso lo quería ver un poco con los de aeros, a ver quién de ellos la tiene Clara con métricas. O sea, lo estoy haciendo todo con métrica con el métrica Explore para ver Bueno qué puedo armar con eso así básico y después según eso Bueno de alguna forma veo como le pego a la Api para obtener el dato y ya está.
13:22 Javier Sankowicz: tengo que
13:29 Mauricio Gomez Quijada: aeros.
13:30 Javier Sankowicz: Pero eso igual no va Sí esa cosa siempre cuando no tengo idea. Lo que sí no sé, es Cuánto puedo demorar eso con eso, yo lo metería una segunda iteración del pádel, por ejemplo. Porque no tiene sino sentido meter un canary si no vas chequeando los errores.
13:52 Mauricio Gomez Quijada: Sí el fin del Cannabis ese detectar si tenemos errores y poder hacer rollback,
13:56 Javier Sankowicz: O sea, dentro de todas formas podemos dejar el canal y automático sin que
13:56 Mauricio Gomez Quijada: si no.
13:59 Javier Sankowicz: tengamos la automatización de los errores para que de última. las cosas que van haciendo con un diplo de producción vayan de esa forma quizás quieren tener una primera interacción sacar el canario, eso no lo sé, tenemos la posibilidad de Eso para mostrar digo, bueno Esto es el plan que tenemos armado con un proyecto
14:14 Mauricio Gomez Quijada: Definamoslo ahora en la tarde.
14:22 Javier Sankowicz: de fantasía que lo podían ser trasladable al megalito y no a los proyectos de Front por el momento. Como que esa es la idea de mí para quedar después de tipo, toma una decisión, o sea, si al final decisiones me dicen Javi hacer lo que a lo que quieras yo agarro el implemento y después le explico a los desarrolladores Cómo usar. O sea, pero el planeta del megalito así ya listo ya no hay vuelta atrás
14:42 Mauricio Gomez Quijada: perfecto
14:47 Javier Sankowicz: chicos, tenemos que o sea le digo y ahora tenemos urgencia porque esto no lo puedo volver atrás. Como que no sé, me encanta esa.
14:56 Mauricio Gomez Quijada: No, no está, me parece bien.
14:58 Javier Sankowicz: Cosa porque si no es como lo explico, No lo empiezan a usar Entonces no lo entienden, pero si lo vas a formar no es la mejor, pero bueno.
15:06 Mauricio Gomez Quijada: Aprenda a nadar mientras te tiro al mar una cosa así, pero es importante
15:10 Javier Sankowicz: Y a mí me hacen eso en general así.
15:11 Mauricio Gomez Quijada: también.
15:14 Javier Sankowicz: Ellos son ellos son más capaces que yo así que lo pueden manejar.
15:18 Mauricio Gomez Quijada: Sí, no, Y cómo se llama estoy no me parece bien ver eso en la tarde y ver también.
15:23 Javier Sankowicz: ojo, lo dije en chiste lo de implementar la primaria y explicarlo después solo por los no por las dudas Mira si pensás que era
15:37 Mauricio Gomez Quijada: no ahí te había perdido un poquito, no No para nada, obviamente estamos claro, en eso, aunque en realidad a mí no me molesta de repente hacerlo, pero
15:43 Javier Sankowicz: Yo en general lo explico un día y le digo bueno Y o sea, le pongo la fecha Si todos. Me aceptan la fecha listo al día siguiente se implementa como que no voy a no
15:54 Mauricio Gomez Quijada: te decía eso habitualmente y y también acordar ahí con farid y poner con con Fer si es que en realidad es importante para el proceso según farid el tema de
16:07 Mauricio Gomez Quijada: De testing bueno que Fer ponga dentro de su calendario y asigne y proteja tiempo para revisar la iniciativa.
16:13 Javier Sankowicz: No, el problema hay que atenderse es que a ver Fer tiene muchas tareas por lo que entiendo O sea yo no, no hablo tan seguido con él porque no sé, no tenemos tanto punto de encuentro. Entiendo que está con tantas cosas que a ver si no le si alguien tipo No dice bueno Esto ponerlo como tu única prioridad. O sea, va por ahí el tema.
16:32 Mauricio Gomez Quijada: Eso sí, claro, absoluto por eso mismo decía que en la tarde un buen momento para decir porque decir Bueno si lo hago de acuerdo a los tiempos que yo tenga o en realidad está dentro de mis verticales o qué sé yo que vea si yo no sé, hay alguien más en cierto, pero parece que está en rimac. Tú entiendes más eso?
16:51 Javier Sankowicz: Sí, es una persona en cuarenma.
16:54 Mauricio Gomez Quijada: Ya sí, sí, pero no sé si en este.
16:57 Javier Sankowicz: Creo que de la dedicación de arriba no me acuerdo ni cómo se llama. Estoy hubo tantos cambios en rima que me perdí.
17:05 Mauricio Gomez Quijada: Pero habría que verlo en una de esas tiene horas bench, ahí en realidad no está con nada, está parado y Fer lo puede pasar.
17:11 Javier Sankowicz: No sabría decirte porque yo casi ya no estoy en prima aquí de hecho mañana es el último día que teóricamente por contrato.
17:17 Mauricio Gomez Quijada: por contra, o sea, técnicamente desde el quince, ya podrías haber ido saliendo a poco y desde mañana ya no está O sea desde
17:25 Javier Sankowicz: Ayer hicimos para armar un par de flujos de diluvio dije Bueno compartí pantalla, lo hacés vos te voy indicando.
17:36 Mauricio Gomez Quijada: Totalmente de acuerdo
17:37 Javier Sankowicz: Lo contrario a una persona que maneja toda la forma, o sea y de hecho él levanta todas las cosas de rajor toda la infra, entonces de fondo tuve dudas es como le expliqué Mirá lo tengo armado así y al final tipo. En modificar los archivos no tardamos nada, tardamos un par de cosas que nos exigen de rima comparar cosas. Y es como en rímac, tiene una mala práctica A mi modo de ver y es que venían a ver Ya de por sí venían se están acostumbrando a Google Cloud venían desarrollando manuales, los desarrolladores tenían mucho permiso dijéramos permisos y imponemos un flujo automático listo armar un flujo automático donde tienen un archivo de claudin para cada entorno en el repo de código.
18:23 Javier Sankowicz: Y separado por ramas, una rama por entorno Y cómo el archivo de Clau Bill de producción en la rama de vivero no sirve para nada, pero bueno, está por ahí. Y aparte en el mismo archivo, deplo y tenés crea la imagen de docker y el Cloud Run y es como si quieres cambiar una variable de entorno, Qué haces volvés a generar la imagen de docker y después ya se hacen cosa que es una paja.
18:49 Javier Sankowicz: Y yo lo tenía nosotros ya lo teníamos separado cosas, pues entonces yo de entrada cumplíamos con derecho de no vamos a hacer de pruebas manuales. Bueno pero ellos aparte tenían una cerveza que usan para todos los servicios de backen con permisos excesivos Bueno ya le sacaron los permisos excesivos Pero siguen usando una sola servicio acá para todos los servicios baquean. Nosotros hacíamos una serie acá por servicio. Porque nada, pero bueno, nos sacaron el permiso para crear servicio.
19:19 Javier Sankowicz: Así que yo le dije mira Tipo le dije a uno consultarle a consultar a Víctor si él si quieren que sigamos la mala práctica usar la misma cerveza Campos para todos los servicios. O si nos dan el permiso para crear una cerveza count y así y asignar los
19:37 Javier Sankowicz: viniles correspondientes. Nada, Eso fue ayer.
19:42 Mauricio Gomez Quijada: Dicen por eso te digo entonces en el fondo ahí hay que ver los recursos Que bueno que tú ya vas a estar desconectado de eso y se sale el cronograma 100% Y el otro es como te digo Fer tendrá que ver si en una es el recurso, ya está bien o tiene algún par de horas venge que lo pueda apoyar si es que no lo está haciendo A lo mejor ya lo hace.
20:03 Javier Sankowicz: Sí yo desconozco completamente No no sé ni qué tareas está Fer ni ni la otra persona O sea no?
20:11 Mauricio Gomez Quijada: Entonces ahí en la tarde nos va a servir un poco para para coordinar eso más que nada farid coordinar eso.
20:17 Javier Sankowicz: y a ver qué quiere hacer, o sea, si él quiere como está el Pablo no se puede implementar, no hay ningún el salvo que él no lo quiere implementar por otro motivo pero
20:28 Mauricio Gomez Quijada: No, ahí revisemos los en detalle. Y y ponemos accionable y vemos el estatus en general. administración y finanzas
20:38 Javier Sankowicz: interno del proyecto que tiene como idea Y hasta tiene pensado algunas cosas meter en algún gcae. Bueno, a ver qué dicen en su momento, a ver que diseñarlo muy bien eso, porque encima ya venimos con gastos excesivos en en Cloud Y ayer en la oficina estuvo hablando con nahuel, que es tipo va mirando las cosas, pero como él no tiene idea de los distintos servicios de gcp, solamente filtra por proyecto.
21:05 Javier Sankowicz: y yo le estoy mostrando un poco de las cosas que existe y las cosas que venimos intentando revisar para Bajar los costos pero nada es como literal falta de coordinación a veces de Por qué nos metemos a alguien del área? no sé qué harías, no sé cómo se llama el bueno, no Por qué no estamos metiendo a alguien de administración de finanzas en el proyecto de bajar los costos de Google Cloud porque ellos son los que ven los costos que tenemos y no sé, se me ocurre ayer tipo nunca nunca hicimos eso, pero tampoco había gente de finanzas pero
21:46 Mauricio Gomez Quijada: Sí ya hoy importante tal vez que más que meter gente al menos nos estén dando
21:53 Mauricio Gomez Quijada: información. O sea que ellos sean los que ellos tienen como dices, tú tienes el historial de pagos tiene el historial de millones de cosas que a nosotros. No nos da más O sea para qué vamos a duplicar el trabajo eso en el fondo, si eso es información que ya existe y está historial y está sistematizada. Para que vamos a darle nuestro trabajo Me parece buena idea.
22:13 Javier Sankowicz: No porque él tiene que hacer un Forecast pero de los gastos para fin de año yo le dije mira Eso depende de talete Entonces esta variante tenés tipo tenés la parte estacionalidad tenés que bajemos los gastos que no lo subamos por otras herramientas nuevas que quieren implementar.
22:30 Mauricio Gomez Quijada: los nuevos proyectos que vienen y todas las otras cosas así que Sí me parece.
22:37 Javier Sankowicz: Escrito en ese documento ni me acuerdo.
22:40 Mauricio Gomez Quijada: No está bien bien Es que en el fondo.
22:45 Javier Sankowicz: Ah lo que sí me acuerdo que lo que para mí no sé el tema de la falta de coordinación en los proyectos que ahora yo sé que lo están laburando Pero hay veces que se nota bastante. Como no sé cuando decís Che quiero encarar esto Ah ya lo está haciendo tal. Y como tenemos suficiente visibilidad de Qué cosas, están haciendo.
23:06 Mauricio Gomez Quijada: Sí, los proyectos, tú dices literarias.
23:10 Javier Sankowicz: No, No necesariamente es como a ver. a ver, yo no digo que me consulten todo, pero no sé la otra estaba hablando con Toby me dice que bueno, me estaba preguntando de algo que dice cheva ni para tal cosa, no sé si meter Esto me estaba apuntando algo y yo digo Sí porque acá tenés pozos Y vos, ya me habías dicho que ibas a usar un raite o un casco o algo de eso como tipo, vas a usar una herramienta específica para cola de mensajes que vas a usar y metieron como opción de que te sirve solo para un tipo de cola de mensaje como medio raro, no sé después no sé si yo no sé qué Qué se supone que está haciendo Nico a nivel Security Es como yo no sé si en algún momento, me estoy pisando con lo que hace él con lo que está investigando.
23:57 Mauricio Gomez Quijada: Justamente primero está en recién en el levantamiento inicio del proceso de Security ahí hay un tag de capacitación de normalización, pero lo estoy metiendo en todos los temas de hecho, lo agregué a la reunión de ahora para que vea un poquito el pipeline. Vea Tú lo ves a él y él te ve a ti Sería bueno, tal vez que coordinaran algo una reunión una Meet para que se pongan al tanto por ahora Nico lo que está haciendo es relevamiento, ya vio.
24:29 Javier Sankowicz: algo de backups tipo de Estamos haciendo, pero no nos sirven de casi nada.
24:34 Mauricio Gomez Quijada: Sí Entonces está en más que nada levantando viendo temas y lo está asociando con lo que está viendo en el en la preparación para la certificación. Cierto que tiene que dar entonces si te puedes reunir con él a ver tema no hay
24:47 Javier Sankowicz: Buenísimo.
24:52 Mauricio Gomez Quijada: por mí, no hay ningún problema. Pónganlo dentro de las tareas. con respecto a unificar proyectos, Sí tal vez estamos llegando muy tarde a esas soluciones Así que tal vez también ahí hay que hacer un
25:05 Javier Sankowicz: Porque por ejemplo yo lo que quiero hacer de monitor y de esto de error del error Rey Bueno también tendríamos que ordenar un poco todo el tema de monitoreo. Para poder entender lo que estamos monitoreando.
25:15 Mauricio Gomez Quijada: Me gusta, me gusta porque debies.
25:19 Javier Sankowicz: Pero no voy a ordenar todo el monitoreo y hacer meter todo en grafán, etcétera y mandar todo con grafán, álva, y que es algo que está investigando antes para hacerlo del pipeline que en realidad es algo muy simple tipo, era parte Pero bueno, es como hay que tener en cuenta que seamos qué vamos a hacer.
25:36 Mauricio Gomez Quijada: Tal vez estaría Estaría bueno que lo hubieras con que les mostraras todo lo que tú tienes planificado, ahí me incluyen. al track de arquitectura en el track de arquitectura está Toby y Joel y ellos han
25:56 Javier Sankowicz: Eso no sé qué está, qué es lo que están haciendo ellos?
25:58 Mauricio Gomez Quijada: Estado Ya Mira te explico rápido hoy día estamos levantando tres tracks principales que son de Box que hoy día se está encargando un poquito de esto de la capacitación para nivelar hacia abajo a los desarrolladores con Mica está el track de arquitectura, que lo están viendo conmigo. Que es en el fondo dar una base a los chicos arquitectural aislada de de Cloud ya ahí te voy a poner en conocimiento del track.
26:26 Mauricio Gomez Quijada: para que lo lo veas que es cosas básicas, sistemas distribuidos cierto ese yo capl etcétera básico para entender no está orientado a Clau y la idea de ello Es que vayan coordinando estos temas con lo que hay en Uma Entonces si vamos a ver alta disponibilidad yo les pido un accionable que vean un componente de Uma donde puedan aplicar estos temas y que lo evalúen bla bla bla bla bla bla y luego ellos hacen un accionable y me lo entregan, lo evaluamos. Lo revisamos etcétera En cuanto al contenido.
27:05 Mauricio Gomez Quijada: Entonces de ahí, una de las cosas que más ha salido y que solo se dieron cuenta. Es lo poco, vamos a decir lo poco amistoso que son las aplicaciones con los diversos niveles de monitorización, entonces en el fondo no sabemos que se está monitorizando y ahí viene un enganche entre aplicación y la infraestructura Entonces sería re bonito que tú hablaras con ellos, te pusieran, le mostraron un poco lo que tú tienes planificado y vieras qué es lo que ellos han levantado en cuanto a aplicación y llegarán ahí una un levantamiento de acuerdo.
27:39 Mauricio Gomez Quijada: lo mismo pasa con
27:41 Javier Sankowicz: una prueba de concepto de mandar los blogs a grafana y que Chau me olvido del login de Google
27:47 Mauricio Gomez Quijada: Te fijas y ahí, por qué Porque arquitectura también arquitectura de aplicación? Tiene que conversar muy bien y tener muy claro, qué es lo que va a arrojar?
27:57 Javier Sankowicz: Sí pero para una primera instancia lo único que necesito de monitoreo en realidad es que tome todas las cosas básicas y es va a ser un sidecar en los
28:05 Mauricio Gomez Quijada: hacia exacto
28:06 Javier Sankowicz: servicios. Después Bueno si querés sacarle jugo Open telemetry Chau ahí hay que meter en código.
28:12 Mauricio Gomez Quijada: Entonces eso sería muy bueno, que lo que lo coordinara si quieres pones una mesa de trabajo me pones como opcional. Y pero lo puedes ver sin ningún problema con con Joel y con Toby y ellos ya han levantado cosas de algunas aplicaciones de de megalito de varias cosas. O sea, Ahí se pueden poner un poco al día y sería bueno así para visibilizar
28:31 Javier Sankowicz: Las agendas no tengo problema.
28:34 Mauricio Gomez Quijada: poner estas mesas de trabajo Te parece cortitas efectivas, pero que nos permitan ponernos al tanto de las iniciativas. Yo la hago No te preocupes, yo agendo esa reunión de trabajo La agenda para la próxima semana te parece?
28:54 Javier Sankowicz: Dale
28:56 Mauricio Gomez Quijada: Y así empezamos de a poquitito si sentimos que te la voy a agendar también con con Nico para que veamos un poco En qué está Nico en tema de seguridad, qué es lo que estamos viendo y unificarlo contigo y con Mica Yo creo que tú ya estás Claro porque tú hablas con mi tú coordinas también con Mica pero si prefieres pongo una tercera mesa con Mica y lo hacemos, No hay problema como tú.
29:19 Javier Sankowicz: Vuelta conmigo de tipo que yo creo que algunos desarrolladores tipo más, no sé que no tienen ningún rol de Liderazgo conocen cosas claras básicas que usan todos los días y otros que tienen roll de Liderazgo no la saben es como digo, ya estamos haciendo esto para los desarrolladores más bajos y ellos ya lo saben y los otros no Qué onda.
29:39 Mauricio Gomez Quijada: Así es Y eso es importante porque eso significa que hay film de gaps cierto
29:41 Javier Sankowicz: tipo
29:45 Mauricio Gomez Quijada: hay.
29:45 Javier Sankowicz: O quizás lo saben, pero cuando realmente lo tienen que usar porque pasó algo pañal entran en pánico, no sé.
29:54 Mauricio Gomez Quijada: Lo que pasa es que ahí hay otra tarea también que estamos trabajando con los líderes es en normalizar las tareas. En que tiendan sobre todos los más Juniors de que si tú estás liderando tú no tienes que hacer todo porque si te pones a hacer todo no tienes tiempo para coordinar y revisar lo que lo que se está haciendo Y eso pierde control. Ahí hay varias cositas, que aunque no creas ya levanté con Santi una iniciativa. Estamos ordenando un poquito los liderazgos.
30:26 Mauricio Gomez Quijada: Ya, o sea, hay mucha cosita que se está trabajando y que cuando ya tenga resultados. Yo prefiero hacer pilotos y cuando hay resultados plantearlo como una iniciativa transversal frente a todos que hablar todas las iniciativas que vamos a levantar y en el fondo después se puede perder alguna y se pierde visibilidad prefiero monitorizar y luego plantear algo más serio. Así que pero el liderazgo había una falta de eso y como dices tú también hay que nivelar y decir. Mira yo espero yo no espero que un líder sea un especialista porque para eso hay especialistas.
31:07 Mauricio Gomez Quijada: pero tienes que saber lo mínimo, o sea, tenés que ser por lo menos hacer Claudio ingenier qué sé yo saber lo mínimo de eso o Claudio
31:15 Javier Sankowicz: Y bueno, ya pasó otra era. Porque era algo lo que dijiste recién y me acabo de olvidar.
31:29 Mauricio Gomez Quijada: entonces
31:32 Javier Sankowicz: no Pasa eso es algo, que dijiste hace un minuto Y te iba a decir algo con eso ya.
31:45 Mauricio Gomez Quijada: bueno, estábamos hablando de las mesas de trabajo de los liderazgos de
31:48 Javier Sankowicz: Sí, no se me fue.
31:51 Mauricio Gomez Quijada: Si te acuerdas, me interrumpas.
31:53 Javier Sankowicz: que Fue nada, pues no sé, tengo alguna cosa que no sé cómo hacer para bueno. Ah bueno no, no era eso, pero bueno relacionado tipo a ver los desarrolladores las células y demás tienen tales Sprint Y qué sé yo y yo como digo bueno que me pongan esto sobre la mesa y me digan Cuándo lo van a hacer, por ejemplo, a ver yo estuve directamente hablando con bonny para un par de cambios para para picar cosas Security y nada, lo fue haciendo a medida que tuvo tiempo esta semana Ahora ya después no tuvo tiempo y nada, pero es como digo, quizás no hace falta que yo tengo una después con Bruno para ver si lo puede hacer él que él que venía manejando consultorios.
32:34 Javier Sankowicz: pero es como si yo digo Che tengo esta cosa de Security Cómo hacemos para decir tipo decirle yo te paso esto Necesito que se lo asignes a alguien en el cambio, me agendas una a mí con esa persona y listo no sé si podemos Porque después terminó perdiendo tres semanas para hacer un cambio de código revoludo, que ya está hecho en varios proyectos. y como que las cosas en general las cosas es que sea depende de que tengan tiempo de hacerlo Pero quizás sí podemos encontrar una forma de decir Che Tengo este esta semana Tengo estos pedidos, o sea, en este Sprint de Security Tengo estos pedidos, no sé si podemos pensar una forma de organizar para Para eso digo para ver cómo cómo nos podemos organizar primero que no sea ni súper burocrático, pero que nos ayuda a ordenar para ser más rápido las cosas.
33:23 Mauricio Gomez Quijada: No, es que eso es lo que tú acabas de decir es el tema, o sea, yo no. Para mí dentro del Sprint y es algo que le converso a la gente en signos También tienen los funcionales y los no funcionales la gente últimamente no sé por qué les digo que en agilidad no existen los requisitos, no funcionales Entonces no el Sprint hay que entregar valor, hay que entregar valor y eso es mostrar en una pantalla y es Mostrar Sí claro, pero no te sirve de nada, si no tiene seguridad te hablan de que los requisitos no funcionales son algo.
33:55 Mauricio Gomez Quijada: Que se tiene que hacer constantemente durante el Sprint O sea si yo tengo que organizar algo Tengo que partir desde la base de seguridad Entonces tal vez mi primera tarea sea configurar los temas de seguridad hacer esto poner autenticación autorization decidir si vamos a ser vamos a ver. Todo eso son mis primeras cartas, porque o si no no puedo llegar al final y ahora enchufémosle un login enchufamosle una autorización enchufamosle un modelo de roles se fijan no, no funciona Entonces es algo que se los digo a los desarrolladores constantemente porque creen que agilidad está Siempre dando solamente pantallas y funcionalidad y no no.
34:38 Javier Sankowicz: No está bien, pero ponerse, no sé si ellos tienen tareas en gira, por ejemplo, tienen los sprints armados, no sé cómo hacemos para decir Che bueno cada una una vez cada semana una vez por Sprint meterme tipo en algo y decirte necesito esto.
34:52 Mauricio Gomez Quijada: Tienen los definition of done y los definition entonces en agilidad tuba y le dice definition cierto definición de de listo que aplica Los criterios de seguridad que están definidos en este otro documento te fijas Entonces le golpeo unenco inexistente está asegurado. Está esto está esto otro te fijas si no, no existe el pan, no puedo decirle Esto está listo porque técnicamente no está listo.
35:19 Javier Sankowicz: No yo te entiendo pero como que lo que te digo es para decir Cómo podemos Modificar un poco la Organización de las tareas para hacer esto.
35:27 Mauricio Gomez Quijada: Bueno hay que hay que hablar con los líderes y con los pm para para estandarizar un poco el el proceso de de agilidad y meter eso también dentro del proceso se pone en el Card como te digo en los yo les pongo definition of done o definition of ready Te fijas y con eso luego defino en otro en otro documento Cuáles son las cosas que yo espero de seguridad si es un en Point tiene que tener esto esto otro, si es un qué sé, yo es una funcionalidad.
36:00 Mauricio Gomez Quijada: Entonces Y eso se define en una de este gastas un tiempo, hay que parar y hay que gastarse un tiempo en definir eso, pero después ya es. reciclar nomás tengo esto tengo esto tengo esto tengo Esto sí listo puedo pasarlo a PR Entonces, pero es una tarea que tenemos los líderes y y project manager más que más que nada.
36:24 Javier Sankowicz: Bueno te voy dejando que estoy demorado para otra reunión.
36:28 Mauricio Gomez Quijada: Dale Javi cualquier cosita, me avisas, no tengas ningún problema, me pones una reunión o si esta semana fue un poco accidentada, pero ya me normalizo en la próxima te mando un abrazo, chao.
36:38 Javier Sankowicz: Dale
  