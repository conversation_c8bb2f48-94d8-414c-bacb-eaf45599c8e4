# 1:1 con Micaela - 28/05/2025

## Información básica
- **Miembro del equipo**: <PERSON><PERSON><PERSON>
- **Fecha**: 28/05/2025
- **Duración**: 37 minutos (13:38 - 14:15)
- **Ciclo de revisión**: Regular
- **<PERSON><PERSON> clave**: Liderazgo, Time Management, Carrera, Reconocimiento
- **Nivel de satisfacción**: 5 - Excelente
- **Seguimiento requerido**: Sí
- **Relación con OKRs**: Objetivos Q2 - Certificación GCP y capacitación inter-equipos

## Seguimiento de acciones previas
- Continuar con capacitación GCP DevOps (en progreso)
- Mantener rol de "traductora" DevOps (exitoso)
- Seguimiento de objetivos Q2 (en track)

## Temas tratados
- Reconocimiento excepcional de evolución en liderazgo y autonomía
- Desafíos de time management y priorización
- Progreso en objetivos Q2 2025
- Evaluación para progresión de seniority
- Técnicas de organización de agenda y Focus Time
- Uso de herramientas IA para acelerar aprendizaje
- Distribución de carga de trabajo en Synapse

## Logros desde la última reunión
- Evolución excepcional en liderazgo y autonomía
- Rol estratégico como "traductora" entre equipos de desarrollo y DevOps
- Actividad de capacitación DEV25 exitosa, demostrando habilidades de facilitación maduras
- Crecimiento transversal con impacto organizacional
- Manejo autónomo de responsabilidades una vez definido el camino

## Logros no reportados
- Impacto positivo reconocido por Santiago en apoyo a sus equipos
- Respeto y reconocimiento transversal del equipo Synapse en la organización
- Capacidad de multiplicar impacto a través de actitud positiva

## Bloqueos reportados
- Dificultad en time management: balance entre 6h estudio + 6h capacitación + 28h Synapse
- Tendencia a planificar bloques de tiempo pero luego hacer otras cosas
- Dificultad para terminar tareas en tiempo estimado (se extienden)

## Desafíos actuales
- Gestión efectiva del tiempo entre múltiples responsabilidades
- Mantener progreso en certificación GCP (ligeramente atrasada)
- Balancear autonomía con solicitud de apoyo cuando es necesario
- Priorización efectiva de tareas diarias

## Bienestar y equilibrio trabajo-vida
- Estado anímico muy positivo: "energías cargadas y con ganas de hacer cosas"
- Quincena anterior fue "vorágine" pero ahora todo más tranquilo
- Vacaciones recientes ayudaron a recargar energías
- Agenda personal cargada pero manejable
- Sin problemas de salud reportados

## Feedback bidireccional
### Observaciones sobre Micaela
- **Evolución excepcional**: Crecimiento en liderazgo ha sido alto y excepcional
- **Autonomía destacada**: Una vez orientada, toma el camino y sigue de forma autónoma
- **Impacto transversal**: Rol de "traductora" DevOps es estratégico para la organización
- **Actitud multiplicadora**: "El conocimiento suma, pero la actitud multiplica"
- **Readiness para Senior**: Técnicamente ya cumple criterios para progresión
- **Facilitación madura**: Habilidades demostradas en actividad DEV25

### Feedback para el líder
- **Excelente Tech Lead**: Feedback muy positivo sobre liderazgo y apoyo
- **Disponibilidad valorada**: Apertura para resolver bloqueos es apreciada
- **Estrategias útiles**: Compartir técnicas de time management fue muy valioso

## Plan de Carrera (Observaciones / acciones)
- **Evaluación de seniority**: Programada para final de Q2 con proceso de autoevaluación + evaluación del líder
- **Progresión a Senior**: Tech Lead considera que ya cumple criterios técnicos
- **Certificación GCP**: En progreso como parte de objetivos Q2
- **Liderazgo técnico**: Evolución clara hacia roles de mayor responsabilidad
- **Enfoque actual**: Prefiere consolidar responsabilidades actuales antes de tomar adicionales

## Métricas de crecimiento
| Habilidad/Área | Estado actual | Objetivo | Progreso |
|----------------|---------------|----------|----------|
| Liderazgo y Autonomía | Senior | Senior | Cumplido |
| Facilitación | Senior | Senior | Cumplido |
| Impacto Transversal | Senior | Senior | Cumplido |
| Certificación GCP | En progreso | Completar Q2 | Ligeramente atrasada |
| Time Management | Mejorando | Optimizar | En desarrollo |

## Recursos de aprendizaje recomendados
- **Time blocking**: Técnica de bloques de tiempo con espacios de 10-15 min entre tareas
- **Focus Time**: Planificación día a día vs. semanal, con flexibilidad para ajustes
- **Herramientas IA**: Usar transcripciones + Gemini para resumir videos de certificación
- **Horas Aeros**: Utilizar 20 horas mensuales disponibles para apoyo en capacitación
- **Apoyo directo**: 30 minutos con Javi para explicaciones vs. videos largos

## Alineación con valores de la empresa
- **Crecimiento transversal**: Aplica desarrollo personal para multiplicar impacto organizacional
- **Colaboración inter-equipos**: Rol de "traductora" facilita comunicación efectiva
- **Autonomía responsable**: Toma iniciativa pero solicita apoyo cuando es necesario
- **Actitud positiva**: Contribuye al respeto y reconocimiento del equipo Synapse

## Objetivos para la próxima reunión
- Evaluar implementación de técnica de time blocking
- Revisar progreso en certificación GCP
- Seguimiento de capacitación inter-equipos
- Preparar autoevaluación para progresión de seniority
- Verificar efectividad de distribución de tiempo

## Acuerdos y acciones
| Acción | Responsable | Fecha límite | Prioridad |
|--------|-------------|--------------|-----------|
| Implementar time blocking con Focus Time | Micaela | 05/06/2025 | Alta |
| Compartir técnica de agenda y ejercicios de respiración | Mauricio | 30/05/2025 | Media |
| Acelerar progreso certificación GCP usando herramientas IA | Micaela | 15/06/2025 | Alta |
| Coordinar uso de horas Aeros para apoyo | Micaela/Javi | 05/06/2025 | Media |
| Preparar evaluación de seniority (autoevaluación + evaluación líder) | Mauricio/Micaela | 30/06/2025 | Alta |
| Focalizar solo en track DevOps en Synapse (sin producción) | Tobias/Mauricio | Inmediato | Alta |

## Notas adicionales
Esta reunión fue altamente positiva, marcada por un reconocimiento excepcional del crecimiento de Micaela en liderazgo y autonomía. El Tech Lead destacó que su evolución ha sido "excepcional" y que técnicamente ya cumple los criterios para ser Senior.

Un punto central de la conversación fue el rol estratégico que Micaela ha desarrollado como "traductora" entre los equipos de desarrollo y DevOps (Javier), facilitando la comunicación y distribución de conocimiento. Este rol se alinea perfectamente con su objetivo Q2 de capacitación inter-equipos y ha generado impacto transversal reconocido por Santiago.

Se abordó en detalle el desafío de time management, donde Micaela expresó dificultades para balancear 6 horas de estudio, 6 horas de capacitación y 28 horas de Synapse. El Tech Lead compartió técnicas detalladas de time blocking y Focus Time, incluyendo la importancia de dejar espacios de 10-15 minutos entre tareas y planificar día a día en lugar de semanalmente.

Se acordó que Tobias focalizará a Micaela solo en el track DevOps de Synapse, sin responsabilidades de producción, para evitar sobrecarga. También se identificaron recursos adicionales como las 20 horas mensuales de Aeros disponibles y el uso de herramientas IA para acelerar el progreso en la certificación GCP.

La reunión concluyó con la programación de una evaluación formal de seniority para final de Q2, donde se realizará un proceso estructurado de autoevaluación y evaluación del líder para formalizar su progresión a Senior.
