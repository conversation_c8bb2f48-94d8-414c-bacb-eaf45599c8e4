# Objetivos Q2 2025 - <PERSON><PERSON><PERSON>

## Período
- **Inicio**: 29 de abril, 2025
- **Fin**: 30 de junio, 2025

## Objetivos de Proyectos (Synapse) - 70%

### 1. Expansión operativa del sistema - 35%
- **Descripción**: Liberar al menos una nueva funcionalidad con valor real al cliente por sprint (cada 2 semanas), en el marco de la expansión operativa del sistema en los meses de mayo y junio.
- **Fecha límite**: 30 de junio, 2025
- **Métricas de éxito**:
  - Mínimo 4 funcionalidades liberadas (1 por sprint)
  - Cada funcionalidad debe aportar valor real al cliente
  - Documentación adecuada de cada funcionalidad

### 2. Integración del primer agente de IA a Synapse - 35%
- **Descripción**: Diseñar e implementar la arquitectura base necesaria para interactuar/integrar el primer agente de IA en Synapse, con una versión funcional en demo al final del Q2.
- **Fecha límite**: 30 de junio, 2025
- **Métricas de éxito**:
  - Arquitectura base implementada
  - Integración funcional con al menos un agente de IA
  - Demo funcional al final del Q2

### 3. Primera entrega a cliente interno - 30%
- **Descripción**: Entregar una versión funcional del módulo de Plan de cuidado de pacientes crónicos a final de Q2.
- **Fecha límite**: 30 de junio, 2025
- **Métricas de éxito**:
  - Módulo funcional entregado
  - Aceptación por parte del cliente interno
  - Documentación completa del módulo

## Objetivos Personales - 30%

### 1. Completar y aprobar curso FHIR - 20%
- **Descripción**: Completar y aprobar curso FHIR en español.
- **Fecha límite**: 30 de mayo, 2025
- **Métricas de éxito**:
  - Curso completado en su totalidad
  - Certificación o aprobación obtenida
  - Aplicación de conocimientos en el proyecto Synapse

### 2. Completar y aprobar una ruta de aprendizaje técnico personalizada - 40%
- **Descripción**: Completar y aprobar la ruta de aprendizaje técnico personalizada asignada.
- **Fecha límite**: 30 de junio, 2025
- **Métricas de éxito**:
  - Todos los módulos de la ruta completados
  - Evaluaciones aprobadas
  - Demostración de aplicación de conocimientos

### 3. Fortalecer capacidades técnicas - 40%
- **Descripción**: Fortalecer capacidades técnicas participando en al menos 1 sesión de pairing y/o code review semanal guiada por un miembro más senior.
- **Fecha límite**: 30 de junio, 2025
- **Métricas de éxito**:
  - Mínimo 9 sesiones completadas (1 por semana)
  - Feedback documentado de cada sesión
  - Mejora demostrable en prácticas de código

## Plan de seguimiento

### Reuniones de seguimiento
- Reuniones 1:1 semanales para revisar progreso
- Revisión quincenal de objetivos de proyecto
- Revisión mensual de objetivos personales

### Documentación de progreso
- Registro de funcionalidades liberadas
- Seguimiento de avance en curso FHIR
- Registro de sesiones de pairing/code review
- Documentación de aprendizajes y aplicación

## Conexión con plan de desarrollo

Estos objetivos se alinean con el plan de desarrollo de Cristian en varios aspectos:

1. **Desarrollo técnico**: El curso FHIR, la ruta de aprendizaje personalizada y las sesiones de pairing abordan directamente la necesidad de "nivelación técnica" identificada en reuniones anteriores.

2. **Confianza profesional**: La entrega de funcionalidades con valor y la integración de IA proporcionan oportunidades para demostrar capacidades y construir confianza profesional.

3. **Transición a Synapse**: Los objetivos están centrados en Synapse, apoyando su transición completa a este proyecto.

4. **Apoyo estructurado**: Las sesiones de pairing con miembros senior proporcionan el apoyo técnico necesario identificado en la reunión del 08/04/2025.

## Notas adicionales

Estos objetivos han sido diseñados considerando:

- La situación actual de Cristian como desarrollador Junior en un equipo de Semi Seniors y Staff Engineers
- Su reciente crisis vocacional y dudas sobre su carrera en programación (reunión 08/04/2025)
- La importancia de proporcionar estructura, apoyo y oportunidades de éxito visibles
- La necesidad de balancear desafío técnico con apoyo adecuado

El enfoque en IA podría ser particularmente motivador, ya que representa una tecnología de vanguardia que podría despertar interés y entusiasmo renovados por la programación.
