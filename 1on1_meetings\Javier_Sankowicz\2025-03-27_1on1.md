# 1:1 con <PERSON> - 27/03/2025

## Información básica
- **Miembro del equipo**: <PERSON>
- **<PERSON><PERSON>**: 27/03/2025
- **Duración**: 30 minutos (16:00 - 16:30)
- **Ciclo de revisión**: Regular
- **<PERSON><PERSON> clave**: DevOps, CI/CD, Personal, Bienestar
- **Nivel de satisfacción**: 2 - Regular
- **Seguimiento requerido**: Sí
- **Relación con OKRs**: Mejora de procesos de CI/CD y DevOps

## Seguimiento de acciones previas
*No se mencionan acciones específicas de reuniones anteriores*

## Temas tratados
- Situación personal (mudanza a casa nueva)
- Sobrecarga de solicitudes de diferentes áreas y proyectos
- Necesidad de apoyo en manejo de prioridades
- Trabajo en CI/CD con cloud deploy
- Necesidad de reunión con CTO para definir flujo, responsables y condiciones de paso a producción
- Implementación de Sonarqube en pipelines (con Micaela)
- Importancia de verificar vulnerabilidades en imágenes Docker

## Logros desde la última reunión
- Implementación de Sonarqube en pipelines (con Micaela)
- Avances en flujo CI/CD con cloud deploy

## Bloqueos reportados
- Sobrecarga de solicitudes de diferentes áreas
- Falta de definición clara de prioridades
- Necesidad de definiciones sobre flujo CI/CD (pendiente reunión con CTO)

## Desafíos actuales
- Balancear responsabilidades laborales con situación personal (mudanza)
- Manejar múltiples solicitudes sin prioridades claras
- Deterioro del estado anímico

## Bienestar y equilibrio trabajo-vida
- Enfoque personal en mudanza a casa nueva
- Estado anímico deteriorado respecto a la última reunión
- Signos visibles de desánimo

## Feedback bidireccional
### Observaciones sobre Javier
- Sobrecargado con solicitudes de todas las áreas
- Muestra signos de desánimo
- Continúa avanzando en proyectos técnicos a pesar de la sobrecarga

### Feedback para el líder/organización
- Necesidad de apoyo en manejo de prioridades
- Necesidad de definiciones claras sobre flujo CI/CD (reunión con CTO)

## Plan de Carrera (Observaciones / acciones)
*No se discutió específicamente en esta reunión*

## Métricas de crecimiento
*No se establecieron métricas específicas en esta reunión*

## Recursos de aprendizaje recomendados
*No se discutieron específicamente en esta reunión*

## Alineación con valores de la empresa
*No se discutió específicamente en esta reunión*

## Objetivos para la próxima reunión
- Revisar estado de prioridades establecidas
- Evaluar resultado de reunión con CTO sobre flujo CI/CD
- Verificar estado anímico

## Acuerdos y acciones
| Acción | Responsable | Fecha límite | Prioridad |
|--------|-------------|--------------|-----------|
| Coordinar reunión con CTO para definir flujo CI/CD | Tech Lead | 1 semana | Alta |
| Establecer sistema de priorización para solicitudes | Tech Lead/Javier | 1 semana | Alta |
| Verificar implementación de análisis de vulnerabilidades en imágenes Docker | Javier | 2 semanas | Media |

## Notas adicionales
Esta reunión de seguimiento reveló varios puntos de preocupación. En el ámbito personal, Javier está enfocado en arreglar su casa nueva para mudarse, lo que representa una carga adicional a sus responsabilidades laborales. En el ámbito profesional, expresó sentirse abrumado por solicitudes provenientes de todas las áreas y proyectos, sin una clara priorización, lo que genera estrés y desorganización.

Un aspecto positivo es el avance en el trabajo de CI/CD con cloud deploy y la implementación de Sonarqube en los pipelines, realizada en colaboración con Micaela. Sin embargo, señaló la necesidad de una reunión con el CTO para definir aspectos críticos del flujo CI/CD, como responsables y condiciones de paso a producción.

Lo más preocupante fue la observación de un deterioro en su estado anímico respecto a la última reunión, mostrando signos visibles de desánimo. Esto podría indicar un riesgo de burnout que debe ser atendido con prontitud. También mencionó la importancia de verificar vulnerabilidades en imágenes Docker, sugiriendo una preocupación por la seguridad que debería ser abordada.

Las acciones acordadas se centran en proporcionar el apoyo estructural que Javier necesita: coordinar la reunión con el CTO, establecer un sistema de priorización claro, y asegurar que los aspectos de seguridad sean atendidos adecuadamente.
