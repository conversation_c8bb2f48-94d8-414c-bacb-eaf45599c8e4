Preparación para Reunión 1:1 - 01/07/2025
<PERSON><PERSON>,
Para aprovechar al máximo nuestra próxima reunión 1:1 programada para el 01/07/2025, te pido que dediques unos minutos a reflexionar sobre los siguientes puntos. 
Esto nos ayudará a tener una conversación más enfocada y productiva.
Por favor, completa este documento.
1. Logros y Avances
Comparte 2-3 logros (reportados y/o no reportados) o avances significativos desde nuestra última reunión. Pueden ser técnicos, de proceso, o relacionados con soft skills.
Separación de repos de API y código (servicio megalith)
Pipeline de Trunk Based Development con Cloud Deploy
Mejoras varias en security (owasp top 10, ingress internal)
2. Desafíos Actuales
¿Qué obstáculos o desafíos estás enfrentando actualmente? Pueden ser técnicos, de colaboración, o personales que afecten tu trabajo.
No tenemos monitoreo y logging centralizado, alertas bien definidas en servicios críticos. Es molesto no tener tiempo ni recurso humano para armar esto y después enterarme que las cosas se rompieron ya fuera de horario laboral.
Si hay 2 eventos / crisis que necesitan de forma urgente alguien de cloud no se pueden resolver ambos. Parte del equipo no está capacitado para resolver sus propios problemas incluso siendo simples. Va a ser objetivo
3. Progreso en Objetivos Q2
Revisa tus objetivos trimestrales y comparte una breve actualización de tu progreso en cada uno.
Objetivo
Progreso (%)
Comentarios



















4. Desarrollo Profesional
Reflexiona sobre tu plan de carrera y desarrollo profesional.
¿En qué habilidades técnicas o soft skills te gustaría enfocarte en las próximas semanas? 
Estudiar para CKA
¿Qué recursos o apoyo necesitas para avanzar en tu desarrollo?
9 horas semanales bloqueadas para estudio
¿Has identificado oportunidades de aprendizaje o crecimiento que te interesen?
5. Colaboración y Equipo
Reflexiona sobre tu experiencia trabajando con el equipo.
¿Cómo ha sido la colaboración con otros miembros del equipo?
¿Hay alguna dinámica de equipo que podríamos mejorar?
¿Tienes sugerencias para mejorar nuestros procesos o comunicación?
Incluir cloud, security y data en early stages de proyectos. Sino llegamos a la entrega con cosas armadas con alambres, y cuando querés solucionar ese problema tenes que coordinar con todo el equipo mientras cada uno está con tareas de nuevos proyectos. O bien, cerras la vulnerabilidad de seguridad, van a con una demo con un cliente, no les funciona y no saben resolverlo.
6. Bienestar y Balance
Tu bienestar es importante para el éxito del equipo.
¿Cómo te sientes respecto a tu carga de trabajo actual?
Siento que en su mayoría resuelvo cosas de Jr, y no tengo mucho trabajo interesante.
¿Hay factores que estén afectando tu motivación o energía?
5 años, sigo siendo el único devops…
¿Qué podríamos ajustar para mejorar tu experiencia laboral?
Contratar más gente y poder encarar proyectos interesantes de arquitectura cloud.
7. Temas Específicos para Discutir
Lista cualquier tema específico que quieras discutir en nuestra reunión.
Cómo hacer que el equipo de IA maneje buenas prácticas y sean más independientes. Mi idea: que los líderes de IA se certifiquen en GCP.
Estimar y poner deadline para que equipo de desarrollo modifique los proyectos de nextjs y que sea la misma imagen de docker para los 3 entornos, así implemento pipelines de cloud deploy.


8. Feedback para mí
Tu feedback me ayudará a ser un mejor Tech Lead.
¿Hay algo que podría hacer diferente o mejor para apoyarte?
¿Qué tipo de apoyo necesitas de mí en este momento?

Recuerda que nuestras reuniones 1:1 son un espacio seguro para discutir cualquier tema que consideres importante. No dudes en agregar otros puntos que no estén cubiertos en esta plantilla.
¡Gracias por tomarte el tiempo para prepararte para nuestra conversación!


