# Plan de Carrera Q2 2025 - Con Especializaciones GCP

## Estructura Jerárquica

CTO -> (Tech Lead (Track Management) y Staff Engineer (track Contribuidor Individual) ) -> Senior Developer -> Semi Senior Developer -> Junior Developer

## Niveles Definidos
- **Junior** (Levels 1-3) - *Exposición gradual a GCP*
- **Semi Senior** (Levels 4-6) - *Preparación para GCP Associate*
- **Senior** (Levels 7-9) - *🎯 REQUIERE GCP Associate Cloud Engineer*
- **Staff Engineer** (Level 5) - Track IC con Especialización Obligatoria
- **Tech Lead** (Level 5) - Track Management SIN Especialización

## Especializaciones Disponibles (SOLO Staff Engineer - IC Track)
1. **Security Specialist** - GCP Professional Cloud Security Engineer
2. **Architecture Specialist** - GCP Professional Cloud Architect
3. **DevSecOps Specialist** - GCP Professional Cloud DevOps Engineer
4. **MLOps Specialist** - GCP Professional Machine Learning Engineer

## Documentos Incluidos

### Niveles Base
- `junior.txt` - Definición completa del nivel Junior
- `SemiSenior.txt` - Definición completa del nivel Semi Senior
- `senior.txt` - Definición completa del nivel Senior (actualizado con req. GCP)
- `staff_engineer.txt` - Definición original del Staff Engineer
- `tech_lead.txt` - Definición original del Tech Lead

### Propuesta de Especializaciones (NUEVO)
- `propuesta-de-especializacion/` - Carpeta con propuesta completa
  - `staff_engineer_especializado_v2.txt` - Staff Engineer con especializaciones (SOLO IC Track)
  - `especializaciones_gcp_resumen_v2.md` - Resumen completo corregido
  - Archivos v1 (versión inicial para referencia)

### Herramientas de Evaluación
- `soft_skills_radar_chart.txt` - Radar chart de soft skills por nivel
- `tech_skills_radar_chart.txt` - Radar chart de habilidades técnicas por nivel
- `resumen_plan_carrera.md` - Resumen ejecutivo del plan completo

## Cambios Principales

### Certificación Base Obligatoria
- **GCP Associate Cloud Engineer** requerida para progresión de Senior a Staff/Tech Lead
- Fundamentos sólidos en cloud computing con GCP

### Especializaciones Profesionales
- **Staff Engineers (IC Track)**: Requieren especialización a nivel organizacional
- **Tech Leads (Management Track)**: NO requieren especialización, enfoque en people management
- **Certificaciones Professional**: Obligatorias en primer trimestre como Staff Engineer

### Cronograma de Implementación
- **Q2 2025**: Preparación y identificación de intereses
- **Q3 2025**: Certificación base (Associate Cloud Engineer)
- **Q4 2025 - Q1 2026**: Especialización (Professional certifications)
- **Q1-Q2 2026**: Promociones con especializaciones definidas