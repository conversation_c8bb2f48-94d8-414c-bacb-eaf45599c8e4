# [ 1 & 1 ] <PERSON><PERSON> / <PERSON><PERSON><PERSON>

  Meeting started: 30/6/2025, 13:00:18
  Duration: 124 minutes
  Participants: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>

  [View original transcript](https://app.tactiq.io/api/2/u/m/r/9uyL3YTDjfXQ4SDuxcRy?o=txt)

  

  ## Transcript

  00:00 <PERSON><PERSON><PERSON>: Cómo estás
00:01 <PERSON><PERSON><PERSON>: buenas como Mauri
00:03 <PERSON><PERSON><PERSON>: Bien, cómo estás tú?
00:05 <PERSON><PERSON><PERSON>: <PERSON><PERSON>, bien, acá andamos.
00:07 <PERSON><PERSON><PERSON>: Có<PERSON> estuvo el descanso, te hizo algo bien desconectarte aunque sea unos días.
00:12 <PERSON><PERSON><PERSON>: Sí s<PERSON>, lo que es hoy a la mañana paella la compu porque si no iba a empezar a
00:19 <PERSON><PERSON><PERSON>: no iba a descansar directamente tampoco es que hice mucho y me quedé acá en el departamento la mayor parte del tiempo, así que fue más que nada. Un descanso para no colapsar.
00:31 <PERSON><PERSON><PERSON>: Eso es lo importante, cómo viene el ánimo la energía la salud primero.
00:37 Cristian Murua: Y bien, bien de salud Gracias a Dios bien. No obviamente renegando con el hombre, pero pero bien en términos generales. Y nada, agarrando ganas de vuelta. De volver a trabajar, así que que bueno está poniéndome al día a poco.
01:01 Mauricio Gomez Quijada: Qué bien perfecto. Eso eso me hace suponer que que vas a seguir aquí con nosotros al menos un tiempito. Esperamos.
01:12 Cristian Murua: Sí sí, la verdad O sea en base a la última charla que tuvimos sobre el tema Bueno ya quedó claro todo, pero la realidad es que bueno en las vacaciones tampoco es que que hice mucho como para decir bueno. Voy a ver qué onda al mercado Así que que por eso tranquilidad transmito tranquilidad al equipo nada me quedó como también la espinita de no haber podido completar antes de irme de vacaciones Así que estoy. También agarrando ritmo para terminar eso.
01:46 Mauricio Gomez Quijada: genial
01:48 Cristian Murua: Y no mucho más. Que una tarea colgada Ahí que los chicos la la resolvieron por suerte y bueno. Ahora arrancando con este tema.
02:00 Mauricio Gomez Quijada: no, genial, Me parece Me parece bien que coordinen ahora con Toby para terminar eso del entregable, netamente más que por Un tema de cumplimiento de recursos humanos que eso es algo manejable por mí es para que tú puedas. Unificar todo lo que has visto y y te genere un impacto positivo si en el fondo si es por llenar papeles los podemos llenar y nos ahorramos todo. el todo el problema aquí lo que importa es aprender aprender y Yo creo que tenemos que estar muy enfocados en esto En este próximo curso sobre todo y el interés q cuatro.
02:47 Mauricio Gomez Quijada: En solidificar bastante tu crecimiento ya si vas a estar con nosotros mi primera tarea va a ser planificar probablemente también con todo, no lo tengo resuelto, pero probablemente, inicialmente con toto, si te sentiste bien y te sentiste cómodo con él. Tal vez proseguir eso y trabajar para el cumplimiento. del del señor si eso es lo que tenemos que buscar Así que yo estoy esta semana para mí es una semana de Sí es bastante burocracia Así que ahí en la medida que los vaya teniendo el
03:24 Cristian Murua: laburo de líder
03:33 Mauricio Gomez Quijada: de ustedes me voy a juntar el quince minutitos, lo vamos a revisar aunque sea muy rapidito, para mí va bien, vas en el cumplimiento de lo que esperamos. En este q Así que no es negativo hiciste bastante y creo que creciste tuviste un bus un crecimiento este q. Que si lo mantenemos a final de año de veces llegar largamente. para senior Perdón para ser mi señor y de cambio señor y Y no tenemos que soltar el el lastre Porque eso significa que si trabajamos rápido, trabajamos bien.
04:14 Mauricio Gomez Quijada: Y vamos creciendo, vamos aumentando en otras cosas y podemos ya para mediados del otro año nuevamente solicitar cambio a neórito y Eso ya depende netamente de ti. de cuánto crezca yo voy a estar acá luchando para eso ya para que Hayan cambios. Y nada, ojalá mantenerte acá lo más posible entiendo también la situación quiero. Quiero ser súper honesto si en algún instante te aparece una mejor opción, créeme que yo voy a hacer. El primero en decirte que te vayas sin ningún remordimiento porque la entiendo. La problemática que tienes la entiendo la vivía alguna vez en mi vida. Y es compleja.
05:05 Mauricio Gomez Quijada: Pero mientras mientras estés acá mientras no tengas otra alternativa y estés acá vamos a estar trabajando para para ir recuperando ese tiempo. Y ojalá podamos tenerte aquí lo lo más posible Ese es mi plan.
05:21 Cristian Murua: Sí pues Honestamente es como lo único que que como que me empuja a mirar para afuera después del resto, la verdad que no tengo crítica o crítica constructiva para hacer la verdad tipo creo que el equipo es genial. El trato genial, la sistemática laburo también como que me siento muy cómodo, ya estarás en casi dos años Ahora en agosto es como que Como que uno se mueve cómodo O sea no, no sea chancha, pero se mueve cómodo dentro de la empresa Pero bueno, qué sé, yo es lo que decís, vos si ahora siento que aparte como que afecta un montón como el incentivo de decir bueno, esta semana tengo ganas de hacer un poco más es como no sé.
06:10 Cristian Murua: Siendo que estoy haciendo como lo justo y necesario y reflexionando esta semana de vacaciones, la verdad es como que aprendí a bueno basta auto exigirte y basta de castigarte y es como que de afuera quizás como decimos acá, parece que te chupa un huevo pero no es como que si no iba a ir. Iba a salir todo bastante mal tipo no iba a terminar de una cosa ni la otra o qué sé yo me iban a ganar los nervios.
06:39 Mauricio Gomez Quijada: No, la cosa es ir encontrando. Es como en todo Cris tuve, no sé aprender a nadar andar en bicicleta. Más tradicional voy a aprender a andar en bicicleta. Tú puedes llevar dos años con las rueditas al lado? Y y tú lo sabes muy bien, tú andas en bicicleta, entonces tú sabes que uno mientras no te saquen las rueditas y te empiecen a forzar, No tú no vas tomando el control del tema, o sea, es como los pañales en los bebés, tú puedes tener a una persona con 50 años con pañales todavía si no se lo sacaste nunca.
07:22 Mauricio Gomez Quijada: En la bicicleta es lo mismo el desarrollo es lo mismo, si si no tienes crecimiento porque yo no te estoy forzando y dándole espacio para que crezcas no te estoy dando el acompañamiento para que crezca y te tengo solamente en el día a día, tú no vas a crecer y puedes pasar 20 años ahí. Y un poquito lo que conversábamos es yo tengo la cultura de que cada uno vaya.
07:49 Mauricio Gomez Quijada: Tomando control de su carrera y vaya exigiendo puch habitualmente independiente el trabajo que yo hago. Yo ya tengo más menos claro, para dónde vamos mientras tú estés aquí, tú vas a tener, vamos a seguir con este plan de acompañamiento Ya esta semana como dices tú para mí es burocrática. Tengo que cerrar todos los feedback doble feedback, porque por un lado tengo los feedback de de disneos que son los que pide recursos humanos.
08:20 Mauricio Gomez Quijada: Pero por otro lado Yo tengo mi propia forma de hacer feedback y de mis propias métricas de feedback. Entonces, abro ahí un poquito el cuaderno y tengo doble tarea recién voy empezando el primero de uno. Y tengo que hacer seis de cada uno, entonces tengo harto ahí. Y pero quiero que eso sea la base también para mientras estés con nosotros seguir en este plan de carrera. Y decirte la estrellita esta que armamos con Santi que tiene varios aspectos técnico y skills de soft Kill ir diciéndote yo te evalúo aquí por esto por esto creo que para que crezcas acá debiésemos hacer esto y ahí ir viendo las iniciativas.
09:08 Mauricio Gomez Quijada: En el q3, para que lleguemos al final de q cuatro es que es la próxima promoción ya muy claritos. Muy avanzados y muy documentados ya ese ese es mi plan, no puedo como dije siempre no puedo prometerte más, pero tampoco te voy a dar menos que eso no. no
09:31 Cristian Murua: aparte O sea convengamos que no sé hasta qué punto era posible y si era posible, yo creo que Faltó poner un poco más de mí de mi lado para tipo completar, por ejemplo la capacitación de Toby de que la promoción haya sido tipo. Ahora era como que era muy j***** Honestamente o sea, es como te pido un feedback temprano antes del
09:54 Mauricio Gomez Quijada: Sí, no te lo digo así Honestamente si tú me presionas hoy. Y tú me dices si eres si yo hacía el vuelo, te te contemplo como un semicidio en este instante consolidado o no que tienes trazas para tenerlo. Si tú quieres, sí Y eso es lo que vamos a trabajar el problema es que yo para la promoción No yo todos para la promoción que es un problema, que te lo comento aquí es un problema, que estamos discutiendo mucho los líderes.
10:22 Mauricio Gomez Quijada: Porque había un poquito de desorden en Uma en todo esto y a lo mejor tú en estos dos años viste que creció gente que a lo mejor según turno debió haber crecido que qué sé yo que no crecieron quienes Podrían haber crecido, qué sé yo. Hoy estamos trabajando con el equipo de líderes para unificar todo esto y tenemos ya una estructura Clara Para mí es obligatorio documentar, o sea, si yo vengo y tomo tuviste el el plan cierto.
10:56 Mauricio Gomez Quijada: tú te hiciste una Bueno ya hiciste el autoevaluación también del del
11:01 Cristian Murua: Sí, sí.
11:02 Mauricio Gomez Quijada: Sí y te hiciste una autoevaluación con respecto a al carrer.
11:08 Cristian Murua: Lo había hecho.
11:10 Mauricio Gomez Quijada: Y y te buscaste como te dije evidencias y cosas.
11:16 Cristian Murua: sí, me las acuerdo, no las anoté como tal, pero
11:20 Mauricio Gomez Quijada: Hazla contrúyete un documentito, porque a mí me sirve Acuérdate que yo llegué mal, Que mal el primero técnicamente el primero de marzo estuve una semana antes, pero primero de marzo todo lo que pasó para atrás y tú ya llevas. Un año nueve meses probablemente más del de lo que antes que yo llegara. Y ese es parte de historia que yo no tengo Entonces para mí es importante.
11:44 Mauricio Gomez Quijada: Lo que te pedí independiente que te lo pedí en algún instante diciendo Mira si tú crees podemos conversarlo con farid con esta base, pero independiente que se conversa o no, con él para mí es importante. porque
11:57 Cristian Murua: eso lo hago Sí igual, o sea mucho Yo creo que quedé muy aislado en ese momento cuando estamos suscripciones y es como que Crecimiento fue eso más que nada o aprender a elaborar. bajo presión pero Hay en cuanto a código o habilidades técnicas y es como que no sé como que terminaba tanto con alambres todo es como que no sé si mejoren técnicamente en esa en esa etapa se aprendí a usar un montón de herramientas por inercia, obviamente, pero es como que no.
12:32 Cristian Murua: No tuve la curva curva de aprendizaje que tuve acá en seis semanas con todo y por ejemplo.
12:38 Mauricio Gomez Quijada: Te fijas eso eso que tú acabas de darte cuenta es lo que nos toca construir de ahora en adelante, porque yo puedo llegar y decir. No sé, voy a decir cualquier tontera, No no es tu caso es netamente para ilustrarlo, pero voy a decir ya a ver, yo voy a mirar semi senior, que es a donde yo te tengo que subir, te fijas y Cecilio me dice, no sé, en comunicación y colaboración dice.
13:02 Mauricio Gomez Quijada: No sé intercambio ideas con el equipo y explica decisiones técnicas y yo vengo y digo mira en realidad no tengo así en la mente ninguna vez que Chris haya hecho eso en el q3, para poder solventar Esto me voy a juntar contigo y voy a decir. Mira, tú vas a estar a cargo? de a ver qué tareas te dio Toby toto, te dio te asignó la tarea x Entonces yo quiero que tú digas Por qué en la próxima Meet en el próximo Daily cuando hagas entrega de tu trabajo, quiero que nos digas Por qué se tomó esta decisión y si tú estás de acuerdo con la decisión que se tomó te fijas yo te voy a estar dando tareas y con esas tareas, yo voy generando Porta Folio y digo Sí él ha participado ha participado tres cuatro veces de hecho participó las primeras tres cuatro y yo se las di y después de ahora en adelante, lo hizo él solo por lo tanto para mí, comunicación y colaboración intercambio idea Está cumplido logra, por lo tanto facilita, el trabajo en el equipo comunica con Claridad y escucha activamente, o sea, si tienes eso último tiene esos cuatro eso a la bestia, te apoya en el segundo ítem, que es autonomía impacto Por qué Porque yo te di la primera parte.
14:16 Mauricio Gomez Quijada: Tú lo hiciste cuatro veces porque yo te dije y después lo hiciste porque tú quisiste por lo tanto gestiona su trabajo con autonomía sabe priorizar en función del impacto ASUME responsabilidad por los resultados y maneja características completas con mínima supervisión. Entonces tenemos que ir viendo tal cual como tú dijiste yo venía haciendo cosas, pero no tenía consolidado, qué hicimos? Le falta consolidar. Te damos tarea de seis semanas o dos meses, Ojalá hubieran sido tres meses hubiera sido genial, pero empezamos tarde con esta planificación de este del cubo anterior y pero lo hicimos igual y tú dices Bueno en estas seis semanas aprendí mucho más a lo mejor que en un año en cuanto a conceptos.
14:56 Mauricio Gomez Quijada: Me calzaron muchas más cosas Entonces eso ese es el trabajo que no solamente contigo, que tengo que ir haciendo con cada uno de los de los miembros del equipo. Porque así es como se crecen en lo en los equipos te fijas. Porque puedes decir Bueno a lo mejor este q no vamos a crecer en nada soft, porque a lo mejor yo te evalúo y a lo mejor digo, no sé.
15:23 Mauricio Gomez Quijada: Yo te veo bien en comunicación, yo te veo bien, no sé capacidad analítica, no te veo mal, no te veo que te falte tanto. Pero a lo mejor te falta más en decisiones técnicas Entonces en este q3, te voy a dar más de nuevo en decisiones técnicas Y probablemente terminemos el q3. Y evaluemos y digamos, no ahora Cris técnicamente está sólido. Está para un Simi senior base. Sabemos todos Que no vas nunca nadie cumple cien por ciento ojo con eso, pero sí estás para promoción técnicamente, pero todavía lo neto Lo noto débil, no sé en comunicación y colaboración, si bien es cierto antes lo vi bien. Ahora siento que podríamos potenciar eso porque es lo que falta para ser más simios más semicidio o simio.
16:12 Mauricio Gomez Quijada: Entonces el Q cuatro uno va y le da con los soft skill y con eso vas equilibrando y al final tomas toda esa información yo tomo se la llevo recurso humano y digo yo quiero proponer promoción de tal persona porque Bla bla bla bla bla bla Y eso es lo que tenemos que estar durante estos cúteres q cuatro. trabajando bastante porque Y tú cuestionándote y auto cuestionándote en el buen sentido no, no autocrítica no autoflagelación crisis, no que decir.
16:49 Mauricio Gomez Quijada: Dame a mí como líder. Tú me tienes que decir siempre esto yo se lo digo a los míos a mis líderes. Dame tres cosas positivas, que yo haya hecho este q y dame tres cosas en las que pueda mejorar. No importa si tú me dices de repente como líder mío. No sé, yo te veo bien en esto, pero tal vez como me dijo por ahí farid y yo le encuentro toda la razón.
17:18 Mauricio Gomez Quijada: Me dijo No sé yo lo forcé y me dijo No sé a lo mejor tú Tú tienes un muy buen nivel de explicación. Pero si te tocara trabajar con gente muy joven de 18 años probablemente los aburrirías porque tú explicas demasiado algo y tienes que aprender a hacer un short sumary y después explicar Bueno te fijas Como apretando uno saca de repente porque a la buena y primera él no se le ocurrió.
17:44 Mauricio Gomez Quijada: Pero si yo lo esfuerzo sale algo Siempre sale algo te fijas siempre entonces por mucho que te digan. No yo te veo relativamente bien te fijas Pero en qué en qué O sea así Honestamente tal cual como me dijiste hoy? Y eso pregúntaselo a todo todo. Pregúntaselo a todos. Con esta dura y honestamente en qué puedo mejorar? Porque a lo mejor a mí Fanny me lo dijo y yo te digo yo no trabajo con gente de 18 años hace mucho tiempo que no tengo equipo milenial. No sé, no sé ya que son Yo tampoco no tengo idea cierto ya el nombre al millennial, generación Z no lo sé.
18:29 Mauricio Gomez Quijada: Que son chicos bastante complicados ahí en sí hace mucho tiempo que no tengo equipos de ese tipo y miembros entonces para mí en este instante no me afecta, pero me lo dejo anotado acá. Se fija y voy a estar trabajando en eso Durante este q A lo mejor con ustedes que no lo necesitan tanto, pero probablemente, entonces voy a hacer un tldr habitualmente. Hay que mejorar ahora viene la explicación larga, entonces Está bien todos tenemos cosas que mejorar constantemente tú tienes que ponerte bastante exigente con los que estemos interactuando contigo.
19:13 Mauricio Gomez Quijada: y luego hacer accionables de eso, o sea Mira no sé Toby me Mauricio toto, me acaba de decir que que tontera puedo decir cualquier cosa que digo demasiado en No sé qué puedo hacer para corregir esto. Ah veamos un accionable y trabajemos en eso. Quiero que esa sea como nuestro nuestro conductor durante el q3 y el Q cuatro. De forma que podamos Oye disculpa, pero es que no sé de dónde sale tanta gente escribiéndome añoro esos primeros días en Uma donde nadie me conocía y ahí está Mauricio no vayan no le importa a nadie ahora todo el mundo ve gente hasta que ni conozco me escribe.
20:08 Mauricio Gomez Quijada: Entonces creo que tenemos que hacer eso te parece qué crees tú que sientes qué piensas? Te vuelvo a decir, lo vamos a hacer mientras tú estés aquí, Ojalá estés de aquí a la promoción y logres aguantar porque no vamos a parar después de eso, créeme que si logras lo suficiente para semicinio. Yo te voy a empezar a puchar para sí señor. o sea, yo soy así Todo lo sabe pregúntaselo Porque viven más más tiempo con él, pero él sabe que yo soy así, yo soy una persona que Hago demasiado puch no demasiado Yo siento que no es demasiado, pero mucho hacia el crecimiento Entonces eso dijiste tú de achancharse que tiende a pasar a mí no me pasa ni a mis equipos, no les pasa porque estoy ya Cómo En qué estás Ya subiste a senior ya, pero ahora tienes que ser estado.
21:04 Mauricio Gomez Quijada: Es que por ahora no me interesa, no, pero que te voy a poner porque tienes capacidad de staffy. Te voy a dar staff y te voy a meter esto y te lo voy a ir acomodando, No te preocupes, yo te voy acomodando la carga Pero para ir adelantando tiempo vi que ahí tiene cinco minutitos todos los días y en esos cinco minutos. Yo te voy a tirar una carga.
21:20 Cristian Murua: No, A ver, no quiero llegar al punto de ser una piedra en zapato, no por habilidad, sino por entusiasmo para para usar esa habilidad. Entonces tipo mientras yo pueda sumar al equipo y hagan una mirada honesta y media seguís sumando no estás O sea no quiero ni siquiera llegar a punto de restar. No quiero dejar de sumar ya cuando dejo de sumar es cuando me digan Che gris empezaba en prolijar linkedin y porque no quiero ser un lastre, la verdad Mientras no aparezca nada así del cielo, Yo voy a seguir estando acá, la verdad es que también. Me queda Justo a poco irme acá sin siquiera haber promocionado una vez.
22:08 Cristian Murua: Creo que que bueno es como que de como te dije antes también, o sea, hago un poco de auto culpa decir Bueno está bien que estabas colapsado Pero quizás, sí. Si ponías un poquito más lo podías conseguir mucho antes Pero bueno no pasa nada por eso y después Sí la verdad. No no tengo nada en contra, me gustaría si en este cutreso 4. O sea, obviamente quiero terminar el bus de Toby Pero cambiar como de mentor.
22:42 Cristian Murua: es lo único que que Quiero probar no tengo nada en contra de Toby Es simplemente que todavía sigue como un residuo de esa sensación de que De que te comentaba hace tiempo de que siento como que Toby me dice cosas que son súper obvias y es como que me hace sentir como que estoy en cualquiera o que no estoy a la altura Pero no por nada, porque yo creo que alguien con la habilidad técnica de todo y el conocimiento y la inteligencia es como que tiende a tipo a hacer eso es como para él es obvio y para mí, no? Entonces como que era mucho cante tipo la diferencia.
23:21 Cristian Murua: de conocimiento de cada uno y es como que lo sentía como tan tan agobiante que había días Que me costaba un poco viste como Hablar con todo directamente entendés, porque yo antes de hablar con todo y
23:31 Mauricio Gomez Quijada: ya
23:33 Cristian Murua: ahora voy a subir esta PR me va a comentar 20 millones de cosas de que esto está mal. Esto está pésimo entonces ya como que iba con miedo lo mismo con los accionables Entonces es simplemente ver si si nada Es que estaba colapsado y esto me sirvió quizás. si veo que mejor
23:52 Mauricio Gomez Quijada: Cambiemos termina el accionable con Toby Te propongo que termines el accionable con Toby revisamos ahí las opciones de gente para para cambiarte ahora. Yo te lo quiero proponer de otro lado. técnicamente tú hoy sigue siendo un Junior Digamos lo que digamos, Dame dame la explicación que sea. Ya por lo que sea sigue siendo un Junior un Junior yo no espero Que sepa nada. Partamos por ahí ya yo sé que tú sabes, etcétera.
24:31 Cristian Murua: No Bueno pero un año y medio acá yo ya como que no puedo.
24:35 Mauricio Gomez Quijada: Está bien, te fijas ahí estás teniendo el problema. Ese es un pequeño detalle Ya te estás autoexigiendo de nuevo libérate, libérate del tema ya. Eres Junior estás catalogado como Junior estamos trabajando para que seas femicidio, hiciste una base. todo lo que tú hagas todo como Junior no tú un Junior Todo lo que haga va a ser siempre altamente comentado altamente cuestionado. Créeme que si en este instante yo lanzara, una PR me llegaría múltiples cuestionamientos etcétera, No te creas que yo me voy a sentar a hacer código y van a aplaudirme todos de pie.
25:23 Mauricio Gomez Quijada: Decir y cómo ve por qué no no estoy metiendo código aquí en en Uma entiendo lo que están haciendo probablemente de repente se me vaya algo. Te diga y van a cuestionar, yo te lo planteo de otra forma. El ciclo creo que lo planteé una retrospectiva y y mica hizo el comentario de que a ella le fascinaba como como lo habíamos propuesto. Vamos a planificar pensar hacer entregar y fallar.
25:54 Mauricio Gomez Quijada: Fallar por qué? Porque vamos a fallar todo es fallar el único aprendizaje viene del fallo Ese es mi experiencia lo que vamos a hacer es fallar rápido. Yo hoy voy a tomar voy a mirar y digo Ah te voy a asignar el aceite veinticuatro que se trata de esto me asignaron ocho horas. Yo le voy a dar con duro, voy a tratar de sacar la funcionalidad en cuatro,
26:21 Mauricio Gomez Quijada: después voy a ser refinamiento rapidito en dos y voy a hacer el PR lo voy a subir. Y una vez que lo suba es paralelo, le escribo a todos los demás le digo chicos. Lo acabo de subir muy a la realidad, por favor, destrócenlo. Te faltó esto. Te faltó esto tomo las otras dos horas, Me faltó. Esto Me faltó esto pum lo entrego. Chao, a la segunda iteración. Tú ya vas a tener claro, va a decir. Ah, Me faltó esto Me faltó esto Este es el mismo escenario. Estoy haciendo lo mismo lo único que es otro recurso es otro lo que sea. Bueno, ahora no me no me olvidaré agregue este ojo. Te faltó esto también Ah de veras. Me faltó ciclo fallar. Fallar rápido muy rápido dejar el ego abajo y el y el auto el autoexigencia porque el autoexigencia es enemigo.
27:13 Mauricio Gomez Quijada: del crecimiento la autoexigencia te estanca es como Yo hice harto tiempo parapente ya me lanzaba en paratente. Y la mayoría de la gente me decía y no te da miedo, sí. Sí, me da miedo el el pelotudo que se esté lanzando es y no diga que le da miedo. Tiene que ir a un tratamiento psiquiátrico, o sea, nos da miedo porque te puede salir cualquier cosa mal. Te cambio un viento. Te descuidaste cinco segundos y te mueres o te quiebras entero quedas cuadrapléjico. El tema es aprender a manejar el miedo. El tema es aprender a manejar el temor el temor al rechazo el temor en general.
27:58 Mauricio Gomez Quijada: Entonces si tú bajas las expectativas de ti mismo Te vas a dar cuenta que vas a crecer más. porque nadie está esperando y si alguien está esperando es bueno también tomar control y decirle en realidad que yo sigo siendo Junior Ahora vamos a trabajar para que yo no sea Junior no sé si me entiende, o sea, es como si tú tienes un un cómo podríamos decir un yanitor un cuidador un conserje.
28:30 Mauricio Gomez Quijada: de un edificio Y Él sigue siendo el conserje del edificio, pero no es el administrador. Lleva veinte años trabajando como conserje, pero es conserje el administrador no puede venir a decirle Oye pero es que tú no te fijaste, no me no te metiste a la oficina de administración y sacaste los papeles que hiciste esto Si tú ya tienes muchos años, pero es que yo no soy administrador.
28:59 Mauricio Gomez Quijada: Soy conserje, para hacer eso tiene que venir usted que es administrador que tiene el cargo de administrador que le pagan como administrador para hacer eso yo tengo 20 años, pero como con Sergio a mí pídame lo que se le pide a un conserje. Te fijas entonces tú tienes que posicionarte también en eso entiendes más el negocio? Probablemente hasta por ahí en ciertas cosas en lo que has trabajado, pero en fire a lo mejor no. En negocio de médico en general no estás eres un Junior con potencialidad.
29:39 Mauricio Gomez Quijada: Que farid te vio y dijo necesitamos porque un equipo tiene que tener de todos los señores, necesitamos un señor prometedor que sea más bajo, pero que tengamos pendiente el crecimiento y que necesite crecer y que pueda estar a la altura del desafío, que necesitamos en cuanto a Innovación en cuanto a otras cosas. Yo entiendo que eso fue lo que vio farid y por eso te puso en el equipo porque es lo mismo que veo yo. Que traes deuda técnica claro.
30:13 Mauricio Gomez Quijada: La gran pregunta es alguien? Se sentó también a orientarte un poco porque Uno puede decir millones de cosas, Tú puedes decir sí estamos en una era de autonomía etcétera Pero siempre hay alguien que te tiene que guiar siempre, tú tienes que fallar y alguien te tiene que estar molestando como te molesta todo y de Oye te faltó esto te faltó esto otro te faltó esto esto podrías haberlo hecho de esta otra forma.
30:44 Mauricio Gomez Quijada: y a Toby también le falta crecer como nos falta crecer a todo en otros aspectos y así nos pasa todo el equipo Cris Te fijas.
30:54 Cristian Murua: Sí y incluso tipo, No quiero que se malinterprete porque no me gustó tipo de la mentoría todo y todo lo contrario era simplemente como eso, Como esos destellos de que bueno, me agobio un poco pero pero no, no tenía nada tipo super agradecido por todo lo que aprendí con él,
31:08 Mauricio Gomez Quijada: No para nada
31:11 Cristian Murua: yo creo que tipo También la autoexigencia mía viene de la mano con La ansiedad de decir Bueno tengo 29 años y es como que la vida pasa.
31:23 Mauricio Gomez Quijada: Te entiendo absolutamente Cris aunque tú no me creas. Yo te entiendo absolutamente pero
31:29 Cristian Murua: Es la espinita que tengo ahí clavada y bueno nada.
31:32 Mauricio Gomez Quijada: Pero te la quiero proponer así. Porque me ha tocado en otros aspectos. Que no te lleguen los treinta y uno de la misma forma. Decir los treinta porque no sé cuánto está de cumpleaños, puede ser que esté mañana y me cagas el argumento.
31:50 Cristian Murua: Todavía Hay tiempo hasta octubre primero octubre, ya tengo 30.
31:54 Mauricio Gomez Quijada: Bueno, que no te pide el el el primero de octubre. Con esta misma sensación que el primero de octubre te pille con la sensación de está bien, no he llegado. Pero voy encaminado y he avanzado y he crecido porque esto Esta inactividad. Aún no se lo va comiendo Cris te lo diré insisto. Te lo digo por por cosa personal y tú dices a los veinte y y el en la preocupación el temor y el día a día te come.
32:29 Mauricio Gomez Quijada: Y te pillan los veinte los veintiuno los veintidós, los 23 de repente miras Patrick pasaron tres años y todos los días decía tengo que hacer algo. Tengo que hacer algo. Tengo que hacer algo y no lo hacía. Entonces a veces preferible un pasito chico todos los días. Esto es una pelotudez pero en realidad suena bonito crecer un uno por ciento todos los días. Hace crecer en un treinta y siete por ciento en un año Entonces si tú te fijas es matemático no es tan así, es mentira Pero es bonito pensarlo así.
33:07 Mauricio Gomez Quijada: Así que yo creo que ahí está el gran trabajo, ahí tenemos el gran desafío los dos, tú controlar un poco. falla falla Cris por favor, falla falla tranquilo y Di No sé si viste alguna vez el el día de la independencia, la película cuando el Will Smith tenía al revés los controles y tiró. Eso es todo Ups la cagué te diga soberizori, debía haberlo hecho mejor. me lo anoto te fijas lo voy a tener aquí y lo voy a estar revisando más acuciosamente pero Ups soy soy Junior soy Junior y Síganme como Junior Así de simple te fijas y tú mentalízate en eso y te va a hacer crecer mucho mucho mucho mucho más.
34:04 Mauricio Gomez Quijada: te va a dejar también más en paz ya, o sea Yo sé que está la desesperación porque estás en una curva así de tu vida, todavía vas para los treinta y yo sé que eso te suena a ti fatal. Fatal Pero créeme que mi más grande de crecimiento se hizo entre los treinta y los cuarenta. Ese fue mi gran crecimiento profesional, si bien es cierto, Yo trabajé desde joven.
34:34 Mauricio Gomez Quijada: Y pico diez millones de cosas a mí los 29 me pillaron te lo voy a hacer así, honestamente Me pillaron boludeando como dirían ustedes. Yo no estaba ahí me ponían en tal pueblo quiere ser. Tú quieres ser el líder? Yo no tenía un Horizonte claro ni nada, ni me interesaba. A partir de los 30 por por como te dije una reflexión muy parecida a la tuya. Dije, no, esto Esto es puro tonteado. Yo tengo tengo que consolidar conocimientos en áreas etcétera y me fui personalmente en mi caso me fui a trabajar independiente y se consultoría.
35:16 Mauricio Gomez Quijada: me especialicé Durante un año a full en múltiples tecnologías en esa época Microsoft y me fui de consultor y trabajaba freelance, no trabajaba para Empresa y conocí millones de escenarios ahí crecí en diez años crecí exorbitantemente Porque si bien es cierto Antes había hecho freelance. Cuando era absolutamente freelance tenía que buscar los proyectos cuantificarlos recursos Buscar recursos de afuera otros colegas ver las Lucas cobrar hacer todo ese circuito y aparte entender el negocio y implementar y liderar y ahí aprendimos mucho, te fijas Pero como te digo 30, no te imagines que en mi caso que probablemente te lo imaginabas.
36:04 Mauricio Gomez Quijada: cierto yo a los 29 años era tiro grito y plata porque es
36:09 Cristian Murua: para mí a los 21 ya está rompiendo la
36:13 Mauricio Gomez Quijada: Era Bueno sí, era bueno porque obviamente al empezar a los 19 eso no te lo puedo negar empezar a los en realidad. Yo empecé como a los 16 a programar pero a trabajar a los 19. Yo ya entendía cosas, pero trabajé en proyectos otros no. Los exploté todos los que debían los explotados aprendí cosas, pero era como Cómo te lo puedo decir era como Luego está bien, nadie te pescaba mucho en esa época te estoy hablando de los 90 Cris los noventa o sea, 91 92.
36:51 Mauricio Gomez Quijada: Ser informático no tenía ningún glamour, o sea, yo no andaba como ahora que la gente te entiende más menos. Te entiende qué haces tú no soy informático Ah Y qué hace? No desarrollo software ya por último te dicen Ah haces página web ya nada que ver, pero igual ya. Antes tú decías era informático y te miraba. Y este huevón, claro, no? Y eso fue mucho Después te decía ya y para
37:16 Cristian Murua: arregla la computadora
37:20 Mauricio Gomez Quijada: trabajar, que a qué te dedicas trabajando informático. Ya veo computadores veo los computadores Ah ya Sí sí, sí, he visto algunos que andan con los computadores en el metro. Entonces nadie entendía nada, entonces yo estaba igual. Yo trataba de de vivir de estar tranquilo de sobrevivir tenía mi hija chica en esa época Entonces está en otra época de mi vida. Era Bueno sí, claro, Porque después de tantos años probablemente si ahora yo te tomo Cuando tú mires para para atrás en tres años más Ponte que yo siga aquí tres años y tú sigas bajo mi cargo de tres años tres años más va a mirar para atrás y va a decir wow.
38:06 Mauricio Gomez Quijada: O sea sí, eso me pasaba a mí sí, entre tanta cosa que uno hace aprende Sí o sí no cierto. Pero suelta Suelta la autocrítica, exígete eso, sí, no dejes de exigirte. Exigirte no matarte son dos cosas distintas la exigencia tiene una diferencia con el auto castigo. Que es la exigencia tiene una meta Y esa es la que tú tienes que tener te fijas Qué es lo que no entiendo.
38:37 Mauricio Gomez Quijada: No entiendo que entiendo ni que no entiendo Bueno vamos a clarificarlo, probablemente eso se hace muy simple. Yo te hago un test una prueba. Y voy a saber inmediatamente, qué sabes y que no sabes si ya sabes qué es lo que no sabes y qué es lo que sabes? Confirmo, qué es lo que tú sabes? Y si al comprobarlo Veo que estás bien, nos estamos en lo que no sabes te fijas es súper simple, no? No tiene más vuelta que eso, o sea cómo logras esto a ver, miremos el código de hecho el mismo código de silents, uno lo toma Mira cierto. Dice Por qué hacen esto.
39:15 Mauricio Gomez Quijada: Por qué no lo hacen de esta otra forma Ah bueno Sí porque hay cientos de formas de hacerla Te fijas en este caso se acordó esto por qué sé, yo cualquier es algo que pasó Mira te lo comento. estábamos en una reunión técnica delante de él estaba todo bien estaba Michael muchas Ezequiel en general Y y Joel hace hace un comentario, fíjate Joel Joel tiene un nivelazo, es un tipo técnico muy bueno.
39:51 Mauricio Gomez Quijada: Y ya hace un comentario que yo se lo dejé pasar, pero yo sé por qué Porque después se lo voy a tomar, se lo voy a explicar él dice. Rimac, por ejemplo el cliente cuando devuelve datos en un servicio web. Los devuelve Así te voy a copiar más o menos la idea del Jason aquí. Y ahí. Adentro le vamos a poner lo que sea.
40:24 Speaker: {
data:[
{
....
}
]
}
40:26 Mauricio Gomez Quijada: Dice, viene la respuesta Jason y ellos, yo no sé por qué. Le ponen Data y meten todo dentro de edad. Por una tontera de ellos. Me imagino que tú lo has visto también hay muchos servicios que te devuelven así los retos sobre todo.
40:47 Cristian Murua: Sí pero eso por el interceptor o no nosotros también lo manejamos de esa manera.
40:52 Mauricio Gomez Quijada: Es por el interceptor Sí porque más que por el interceptor esto tiene una explicación arquitectural. Si yo llego y digo Abre mi llavecita Jason cierra mi cita y pongo nombre dos puntos edad dos puntos cierto y meto mis propiedades. cuando yo tengo que cambiar estructuras se impacta más y tú decir Y por qué se impacta más, porque no tiene una agrupador entonces para eso el open punto org recomienda que las respuestas sean mensajes y adentro los mensajes Data y adentro los temas de Data error y adentro de los temas de error Te fijas es por un tema de agrupación porque arquitecturalmente. Te ordena visualmente Y si tienes que cambiar algo es como tener.
41:53 Mauricio Gomez Quijada: En vez de tenerte Perdón String nombre String dirección en vez de tenerlos así dos métodos dentro de una clase que se llame persona y le meto propiedades Luego si tengo que cambiar cambio la propiedad de persona y puedo manejarlo de mejor forma. Esto es lo mismo entonces te fijas Como tipazos, ya estaba Toby también. Entonces yo me reí No más yo les mandé un Jajaja
42:24 Cristian Murua: la de hoy
42:25 Mauricio Gomez Quijada: Un bonito te fijas cuando el el Joel dijo Yo no sé, esto es por pura tontera de de Perú de rimac y yo Jajaja le mandé un bonito así riéndose te fijas porque
42:33 Cristian Murua: de los
42:39 Mauricio Gomez Quijada: yo sé que no es por pura tontera. Y yo podría decir ustedes como senior o con personas con conocimiento de senior debiese saber por qué se hace? Te fijas te fijas que a todos nos falta algo porque no todos hemos vivido todos los escenarios y no hemos vivido todo lo las arquitecturas posibles A mí también me falta. Mucho millones de cosas Entonces quiero que te y Alguien podría decir siempre tú ya es treinta y tantos años, sí. Y probablemente Si trabajara, doscientos años más seguiría sin saberlo.
43:18 Cristian Murua: Aparte todo va cambiando con el tiempo, así que aparecen cosas nuevas.
43:23 Mauricio Gomez Quijada: Entonces qué a qué voy con todo esto libérate del autopresión y súmate a la exigencia no a la sobreexigencia a la exigencia a decir. Estoy en este estado o Quiero saber en qué estado estoy o sabes que este Mauricio te voy a poner 15 minutos más otro día porque en realidad estoy naufragando, No sé lo que sé, no sé quién soy No sé a dónde voy, no sé si la vida es o no es Eh si si se es o no sé ese si la razón la tiene Descartes o kit que dar o can qué sé yo si te pones filosófico, No no sé nada de nada, Solo sé que nada se decía.
44:07 Mauricio Gomez Quijada: Y quiero en estos quince minutos aclararlo, no hay ningún problema si estático toda esa libertad. Pero tienes que aprender a vivirla tú. Tú Cris tú tienes que aprender a decir, yo me voy a liberar Pero yo no te puedo liberar de esa carga esa carga te acompaña todos los días cuando tú te duermes. Ya sigue tú tienes que venir y decir. Ah, y te insisto tú vas así.
44:34 Mauricio Gomez Quijada: Y yo ya vengo así ya los cincuenta son donde uno empieza el declive de hecho me atrevería a decir que a los cuarenta y cinco uno ya llegó a la meseta. Te quedan quince años todavía para explotarlos tranquilamente y créeme que en un año si uno quiere y se lo propone en un año uno puede explotar y crecer como tú quieras. Pero depende de ti vas a tener días Muy buenos y vas a tener días muy malos.
45:03 Mauricio Gomez Quijada: Como como dice por ahí un filósofo dice humilde en el triunfo y digno en La derrota. O sea, cuando te toquen días muy buenos no soy el mejor cuando te toquen días muy malos no soy el peor. Eso anótatelo, grábatelo a fuego, imprímelo y pégalo al frente de tu de tu espacio de trabajo en tu cama cuando te vayas a dormir etcétera Cuando gane, no soy el mejor y cuando pierda no soy el peor Y eso te va a dar una tranquilidad increíble.
45:43 Mauricio Gomez Quijada: Y eso es lo que tú tienes que trabajar y lograr si logras aquietar aquí todo va a andar tranquilo. Por qué Porque siempre te va a venir algo cuando solucionemos tu señor y ti te puede venir cualquier otro problema y va a decir Bueno ya estoy más tranqui, un poco más tranquilo en plata. Pero no sé, ahora me nombraron semicidio y no estoy seguro y viene el síndrome del impostor, no estoy tan seguro de ser si mi señor. Entonces la tranquilidad es algo que tú tienes que lograr de a poco.
46:16 Mauricio Gomez Quijada: Okay trabajarlo mentalizarte en eso bien. Lo dijiste si no se descontrola todo pierdes el Horizonte y a las finales cuando te das cuenta las cosas no son tan malas como parecen y las cosas tampoco son tan buenas como parecen. O sea, el pasto del vecino no es tan verde como uno cree ya todos tenemos. Te lo digo porque a mí me pasó algo parecido alguna vez en mi vida, te lo digo.
46:49 Mauricio Gomez Quijada: Me pasó y tomé decisiones y y me descontrolé y después mirado en perspectiva. No era tanto, o sea, era mi autoexigencia que me decía fallaste. Fallaste fallaste fallaste y y está bien fallé. Bueno, probablemente nos sentemos y llegamos a la conclusión Sí Mauricio falló. Y qué importa se murió alguien no se fue a la guerra de la empresa? No? Aunque se haya ido a la quiebra la empresa te fijas, quién alguien no ha fallado alguien?
47:28 Cristian Murua: Eso a ni siquiera haberlo intentado.
47:31 Mauricio Gomez Quijada: Te fijas, o sea, absolutamente eso creo que también yo lo digo siempre. digo si me hacen elegir entre alguien que no ha fallado nunca ese ese tipo o es Dios Si me dice yo jamás, he fallado o es Dios cosa que encuentro muy improbable o es un mentiroso? Te fijas Porque todos. Hemos fallado todos todos hemos fallado y todos vamos a fallar. Lo importante es cómo lo asumimos Cómo mejoramos, cómo rectificamos fallé Por qué fallé? Fallé porque no tenía muy claro Cómo funciona la arquitectura de nest.
48:11 Mauricio Gomez Quijada: Perfecto entonces pongámonos a estudiar nest. Y si y te pone y practicas ahí cinco minutitos todos los días diez minutitos quince minutos, yo siempre le propongo a todo el mundo. A mi equipo anterior le proponía esto que yo no les puedo exigir nada. Les propongo que se queden quince minutos más después del fin del horario del trabajo viendo algo Yo voy a estar aquí conectado si necesitan ayuda me hablan no más de 15 minutos 15 minutos se lee en un artículo hacer un código mejoran algo ven una PR Cualquier cosa que les ayude a crecer se hacen eso laboralmente son doscientos días en el año aproximadamente.
48:57 Mauricio Gomez Quijada: Así que no más hiciste doscientos crecimientos leíste un artículo viste algo leíste la documentación de de nest te fijas viste como se implementa tal cosa Me metí al repositorio de nest a ver cómo lo implementan Cómo se implementa esto Cómo hace Qué es un interceptor. Esto es una pregunta y yo le hago a todo el mundo en general. Tú conoces los decoradores? Cierto, Sí tú sabes cuál es el fin de un decorador puedes decir sí o no? Te voy a decir lo mismo te voy a tratar como si fueras un alumno en mis capacitaciones, tú eres informático. Yo soy informático, no hablemos de título, me da lo mismo tú eres informático, pero trabajas en informática. Yo soy informático Porque trabajo en informático.
49:44 Mauricio Gomez Quijada: Cero y uno si yo te pregunto algo a ti cero y uno lo sabes o no lo sabes si lo sabes, me lo respondes, si no lo sabes, te lo explico cero y uno para mí, si tú me preguntas algo a mí cero y uno si yo lo sé, uno te lo respondo. Si no lo sé, te digo, lo voy a investigar y mañana te lo respondo con esa con ese contrato, no vamos a tener jamás problemas. Se aplica entonces dado ese contrato, Tú sabes lo que es un decorador.
50:12 Mauricio Gomez Quijada: Cuál es el fin de un decorador.
50:14 Cristian Murua: El decorador lo que hace es interceptar una request dentro del controlador de nes. para limitar o Habilitarte En caso que necesite permiso. la riqueza ejecutarse
50:31 Mauricio Gomez Quijada: Perfecto, me lo diste en el entorno de nest ahora yo te digo otra pregunta. Te digo Tú crees que fuera de nest o net o lo que sea ex de cualquier framework existe el concepto decorador.
50:44 Cristian Murua: Sí pues al fin y al cabo es como
50:47 Mauricio Gomez Quijada: Así es eso, eso más que un middleware que es la parte operativa si nos vamos a orientación objetos se llaman anotators anotadores Y esos anotadores existen en orientación a objeto. Son parte de la orientación a objeto y esa es una forma de meter funcionalidad enriquecida a una clase, si tú te fijas cuando tú le pones un decorador algo ese algo es parte de una clase, puede ser una clase un método lo que sea enriquecen las clases enriquecen los métodos con comportamiento.
51:24 Mauricio Gomez Quijada: Ahora yo les digo usando Java Script o type Script o lo que sea quiero que implementen una clase que tenga un decorador pero no un decorador propio de type Script quiero que ustedes inverten un decorador y se lo inyecten a una clase de Script Si logras hacer eso vas a entender Cómo funcionan los decoradores. No solamente en nest Entonces te fijas. Eso eso es lo que uno tiene que hacer habitualmente esto debe existir en algo.
51:59 Mauricio Gomez Quijada: Yo yo lo planteaba así O sea en principio no era así Por qué en los noventa yo te voy a ser súper honesto y nos estamos extendiendo, pero no importa, quiero dejarte muy claro todo esto. Cuando yo aprendí a desarrollar, cómo sería? No tengo nada, aquí voy a buscar uno. Yo tenía un solo libro que era un libro como este así de grueso. Te fijas. Era toda la documentación que yo tenía y se llamaba.
52:37 Mauricio Gomez Quijada: introducción al C y decía objeto clases propiedades métodos después Aquí como escribir en un archivo que es un hilo y aquí avanzado ya así como multi hilo y cuestiones que en esa época yo. Qué m***** esto de dónde salió esa cuestión semáforo? Con eso aprendí a desarrollar te fijas. Cuando aprendes a desarrollar Así luego cuando viene y me dicen cualquier otra cosa, yo digo. Esto podríamos decir que es como un parecido a tú has programado en Este lenguaje, sí, como Esto sí es lo mismo, es lo único que hicieron una variante Ah perfecto.
53:31 Cristian Murua: Pues ya tipo toda la semántica ya la tenés.
53:34 Mauricio Gomez Quijada: Entonces cuando viene y te dicen vayan inyección de depende Bueno lo que estábamos conversando el otro día tú no estabas y todo tiene una frase que es muy cómica y yo siempre me río. Pero no es tan efectiva no es tan real. Él dice pronen lo complicaron los mismos por qué quisieron hacerlo como baquent y se trajeron toda esa porquería del paquete de de las clases y todo para el Afán y yo me reí, pero el otro día Les dije sí, pero no voy a aclarar esto porque no quiero que quede tan tan al aire Esto no es tan así.
54:16 Mauricio Gomez Quijada: Porque antiguamente que existieran los frameworks como angular como Bueno acumular un framework, no las librerías como react, etcétera. Fronner era una pesadilla era una pesadilla o sea trabajar con Jake cuery. trabajar en javascript con prototives
54:41 Cristian Murua: En el html.
54:45 Mauricio Gomez Quijada: los esqueletos y toda esa cuestión era Una pesadilla los fronés no se complicaron porque quisieron complicarse. Si tú en esa época que yo vine porque yo soy lo que sería hoy día full stack Pero es mentira. No existe full. Está uno siempre tiene una especialidad. Yo entiendo Pero no es mi especialidad, pero lo entiendo si en esa época tú me hubieras dicho que yo podía hacer esto del storybook de tener ahí lo lo los componentes. Yo hubiera llorado de emoción.
55:22 Mauricio Gomez Quijada: Sinceramente me hubieran visto llorar de felicidad y emoción para ustedes hoy día es normal. Pero para poder lograr eso hubo que complicar ciertas cosas, porque si no, no llegamos, no antes era cero reutilizable, si tú lo viviste o lo viste alguna alguna vez con era prácticamente imposible reutilizar nada, tenías que partir todo de cero. Te fijas.
55:51 Cristian Murua: etiquetas seleccionar la etiqueta algún evento que
55:56 Mauricio Gomez Quijada: Y El tipo se equivocó Y en vez de ponerle red Quest le puso red se te fue al c***** porque el abuelo no la pesca porque le faltó una s que no deja Y dónde está la cuestión y vamos a depurar y vamos para acá hasta después de los siete ocho días te das cuenta. Que lo parió me dio la dislexia ahora. Rápidamente, gracias a esa complejidad.
56:24 Mauricio Gomez Quijada: Entonces te fijas al al punto que voy en el fondo y que como dice farid, yo soy muy bueno alargando las cosas pero Pero quería que quedáramos muy claro. De que el conocimiento es algo que se reutiliza en general que en informática se ha inventado muy poco en los últimos años. Y que todo viene de bases, si tú entiendes orientación objetos, vas a entender muy rápido en esto. Te fijas porque todo es Qué qué es un middle es un interceptor.
57:01 Mauricio Gomez Quijada: Punto No interceptor. Chao, es un patrón que existe antiguamente le decíamos de otra forma, se me olvidó el nombre. pero Pero era es un getway, toma ahí entre medio cierto y muy cierto algo y lo procesa chao, tiene pros tiene contras. Y eso es lo que yo siempre le pregunto a todo interceptor Sí perfecto y qué contra puedo llegar a tener con el interceptor nada? Qué cosa no harías tú en un middleware, por ejemplo, eso es algo, es algo obvio Porque si yo tengo un miedo el cuerpo y en ese middleware, yo tengo que ir a buscar a la base de datos, Yo tengo un problema.
57:41 Mauricio Gomez Quijada: Tiene que ser lo más autónomo posible, por qué Porque detiene el proceso al llegar el rango es el toma el control detiene todo lo demás si este se demora cinco horas va a estar cinco horas el proceso detenido a darme en todos lados, se una las desventajas del proceso interceptor del patrón te fijas. No es del patrón Bueno del entorno también, pero del patrón lo que te acabo de decir Ah bueno tenemos esto.
58:13 Mauricio Gomez Quijada: Dame todos los te estoy diciendo tontera, pero para que entiendas cosas que yo veía con mis chicos antiguamente, Dame todos los componentes de una clase. Te voy a dar uno nombre de la clase y propiedades de la clase, bueno y empezar. Bueno sí, nombre propiedades método. Y ahí Qué más, Qué más tiene una clase. Te fijas tiene más cosas la clase absolutamente a clase, tiene propiedades métodos constructor Destructores eventos, etcétera, etcétera al menos tiene esos cinco componentes. Cuál es la diferencia entre un evento y un método. De no cosas que yo te puedo decir que son simplísimas. Para mí.
59:02 Mauricio Gomez Quijada: probablemente puede hacer trastabillar hasta Joel o a Toby Cuál es la diferencia entre un evento y una y un método de una clase. Que el método es parte de una acción habitual de la clase y un evento es un método circunstancial. ejemplo súper simple acelerar frenar estacionar son métodos de la clase Auto chocar Es un evento de la clase de auto, porque a pesar que es un comportamiento nadie se compra un auto para chocar, pero sí le puede pasar si se unen otros eventos de otros de otras instancias.
59:42 Mauricio Gomez Quijada: Entonces te diga todas esas cositas te ayudan porque uno de repente empieza a entender y dice. Y esto del event, Qué es un evento Y a quién le pertenece a la clase que Lance
59:51 Cristian Murua: claro
59:57 Mauricio Gomez Quijada: el evento? Y siempre en un evento, tú tienes una clase que lanza que hace el Flow del evento y una que se suscribe un suscriptor. Y dice entonces hay una clase que puede estar escuchando Sí y eso no es el patrón observer Ese es el patrón observer el patrón observer es una clase que se suscribe a los eventos de otras clases. Y esos eventos que son métodos circunstanciales que puede tener la clase, ejemplo inventario.
01:00:33 Mauricio Gomez Quijada: La clase inventario o stock puede tener un evento que se llame out of stock Nos quedamos sin stock? Y ese se puede disparar si es que le ponemos un un cierto un límite de cinco entonces si tenemos cinco o menos unidades de eso. Cierto se disparará el evento y qué hace la clase, dice. Pasó algo? Quién lo maneja allá arriba y qué es lo que haga y me da lo mismo yo grito y aviso, chao. Y arriba tienes que tener un Server cierto un suscriptor.
01:01:12 Mauricio Gomez Quijada: Que si te fijas igual que el patrón suscriptor que tiene wallpapsap que tiene gcp. Estoy nombrándote cuarenta cosas de una cosa que es básica que se se cubrió y se habló por los sesenta setenta. Entonces todas estas teorías te ayudan a entender los frames las cosas y ay
01:01:34 Cristian Murua: sí
01:01:41 Mauricio Gomez Quijada: dependen sin te fijas Ah y patrón de inyección de dependencia que te permite desacoplar. Cierto y bajar la dependencia de las clases bla bla bla bla bla bla bla, te fijas eso es un patrón. Lo tienes en nest, Sí claro, obvio por defecto. ahora Yo hago la segunda pregunta. Cómo funciona la inyección de dependencias en Por qué Porque alguien tiene que mantener el estado de cómo tú haces la inyección de dependencia? Si tú lo has hecho manualmente fuera de nest, ponte No sé, en type Script yo creo puro y duro. Tú te vas a dar cuenta que si tú tienes un proyecto de type Script tienes que agregar cierto componentes adicionales para tener inyección de dependencia.
01:02:39 Mauricio Gomez Quijada: Tienes que poner ciertas librerías de tercero y tú decir y por qué necesitas esas librerías de tercero? Porque son las que manejan las instancias de la inyección de dependencia. Yo tengo que decir tengo la clase inventario y esa clase inventario es singleton, es una sola. Y tú la tienes que definir al principio cuando se lanza, la aplicación busca y instancia un inventor y déjalo en memoria. Cuando alguien lo inyecte o inyecte un inventory una interfaz inventory.
01:03:15 Mauricio Gomez Quijada: Tú tomas esa instancia Y esa es la instancia que pasas O sea tienes que tener un motor de manejo de inyección de dependencias atrás. En nest, tú no te preocupas de eso porque nest trae ese motor incorporado. Pero existe ese motor entonces tú vienes Y dices Ah entonces cuando yo paso una dependencia. Cuando paso, por ejemplo una interfaz Yo podría definir que esta interfaz es singleton o es múltiple o puede tener múltiples instancias, etcétera claro.
01:03:50 Mauricio Gomez Quijada: definitivamente Tú le tienes que decir cierto que esa instancia que tú le estás pasando o le puedes decir en el configurador de nest en un principio, lo puedes hacer, sino por defecto, creo que ASUME que sigue el Doctor le tiene que decir esto es múltiple y puedo tener múltiples instancias de esto y Cómo sé que le voy a pasar porque le pasó la instancia específica del del interfaz. Yo puedo tener la interfaz uno interfaz dos interfaz tres.
01:04:19 Mauricio Gomez Quijada: Que todas son instancias distintas de interfaz Entonces le digo a esta el inyecto a la interfaz, uno este otro. Te fijas Como todos esos temas son los que uno tiene que llegar y decir. Sabes que hoy día me voy a quedar quince minutos voy a pescar esto en lo que dijo este chileno que lo tuvo y voy a ver si es verdad y voy a empezar a hacerle Mauricio sabes que aquí no quede tan claro, como lo estás Cómo dijiste tú que había que hacerlo y probablemente no aceptemos y lo veamos 30 minutos y saquemos Y si que no lo sacamos me llevaré yo a la tarea y después la revisaré y dile Ah pasó por esto y mañana lo revisaremos.
01:04:58 Mauricio Gomez Quijada: Esas son formas de crecer. Te fijas Yo estoy aquí. Yo estoy aquí y Mientras esté aquí puedo apoyarles. No se preocupen, si se quedan quince mil Yo también me quedo a veces quince minutos media hora más después de las seis de ustedes, cinco mías. Quedo aquí trabajando porque tengo que cerrar cosas. Siempre estoy ahí disponible, estoy viendo esto. Te parece buena idea? Te parece mala? Te daré mi comentario lo discutiremos.
01:05:30 Mauricio Gomez Quijada: veremos código así se crece Cris Ya te lo dejo como desafío, como iniciativa te vas a frustrar, vas a radiar porque esto es así. Este es un área que es el 99% de frustración un uno por ciento de De media felicidad a veces y dura poco, así que no te calientes la cabeza, por eso no te estreses. mantén la tranquilidad y créeme que si Lo vas haciendo de A poco, te vas a ir dando cuenta de cómo creces te vas a dar cuenta y lo vas a notar? Y lo otro y lo último aprende a decir No entendí. Yo se lo digo constantemente a farid.
01:06:22 Mauricio Gomez Quijada: empieza a explicar de repente y yo digo para llevo tres meses en No tengo ni idea lo que me está hablando esta cuestión de falla, sí, entidad al principio, ni lo entendía habló paraba por eso, o sea falla el Qué falla. Ah bueno el estándar Ya, pero es un estándar de quefort porque hay cientos de estándares tiene estándar http. Y eso es un documento y chao. Bueno, no Este es un estándar más completo porque tiene implementaciones porque tiene servidores que se han implementado sobre esto y que Tú los puedes utilizar para esto ya ya, o sea, es un estándar más completo y en maduro en ciertas cosas maduros en otras cosas no? Ya ahora sí, déjame estudiarlo. Pero de repente están hablando.
01:07:13 Mauricio Gomez Quijada: Y también quedó así O sea hoy día estaban hablando de value set y expando. Tabamaico estaba todo bien, estaba Joel y estaban ahí. Y después cuando terminaron me preguntaron Y tú qué opinas si yo me quedé en el Buenos días. O sea, le Soy súper honesto, Yo me quedé en el Buenos días, No tengo idea lo que están hablando probablemente después lo revise. Háganme la pregunta específica, qué crees tú que es mejor a o b Cuáles son los pro y los contras de a y b. Esto entonces listo no necesito saber más tampoco.
01:07:56 Mauricio Gomez Quijada: Me lo dejo como tarea y lo Aprenderé y lo entenderé después, pero no te creas que a mí no me pasa también me pasa. Pero paro y digo no entiendo y no sé si es importante en ese instante para yo
01:08:08 Mauricio Gomez Quijada: hacer mi trabajo. Entonces tú también no te sientas con la obligación de tengo que saberlo. parte con una frase que te libera de culpa Disculpen si el tonto soy yo o no entendí bien pero
01:08:22 Cristian Murua: No entendí
01:08:23 Mauricio Gomez Quijada: No entendí probablemente el toto soy yo estoy aquí, pero a lo mejor no me perdí de algo Yo siempre digo lo mismo me echo la culpa yo todo se ríen en el peor de los casos dicen ya Bueno sí, ya eres pelotudo y te lo voy a explicar ya, vale? Si yo lo que quiero es que me explique nada más. Entonces te fijas Y eso es válido por dos o tres veces, o sea que otra vez No te entendí. Vayamos por parte a ver entiendo que al principio fue así cierto.
01:08:52 Mauricio Gomez Quijada: Estamos claros en eso, sí o no, Eso es así, sí, eso es así ya perfecto esta otra parte no la entiendo cómo llegó el gato de estar allá en el barrial, hasta parado aquí a esa parte no la caché no, que el gato mientras tú miras y el barro saltó Ah ya bueno saltó y ahí pegó el Salto y llegó acá va perfecto, ya ahí me cuadro y cómo termina esto acá ya y después manguerearon a los gatos y listo.
01:09:17 Mauricio Gomez Quijada: Pero pierden el miedo a eso no tiene la responsabilidad. Yo no la tengo nadie la tiene. Pasa a veces que nos juntamos todos estamos hablando lo mismo y todos entendemos Sí pero nadie tiene la obligación. De entenderla. Entonces no sé qué piensas tú.
01:09:36 Cristian Murua: yo también estuve bastante perdido en la 1000 de esa y es como que bueno después en la de los chicos como que Lo agarró y le dije una cosa, Yo tengo tareas que competen a esto que estoy moviendo. No entendí un pepino y me dijo No no tranquilo, vos tipo que solo consuman el recurso y lo tratas de dejar encarado para futuro, o sea como que no es que no seamos a poner implementarlo ahora así que qué bueno, pero sí se puede ponerlos a leer más más detenidamente revisarlo un poco mejor y bueno. De última levantar la mano y preguntar las dudas que hayan quedado.
01:10:21 Mauricio Gomez Quijada: sí, sin ningún problema, o sea A lo mejor no es la distancia y a lo más te van a decir, juntamos los quince minutos después y lo vemos. Te fijas y listo, Sí hijo usted me lo des lo vemos y listo y Quítate la culpa por la culpa de un muy mal consejero. Te lo digo Honestamente te impide crecer te impide dormir, te impide vivir. Quítatela, no.
01:10:52 Mauricio Gomez Quijada: mira Te lo voy a decir así uno es Junior y te pagan como Junior para lo que pagan como Junior no vale la pena la culpa la culpa empieza a tenerla cuando seas líder cuando sea manager ahí empieza a tener un poco más de culpa porque Ah por último uno hasta en un Rango más decente.
01:11:11 Cristian Murua: Nada, pero tampoco, o sea, quedarme la cómoda decir Bueno ya está, no me quiero lavar las manos diciendo no. Bueno, es que es por el señor y es como que quiero. O sea que sientan que me lo merezco el ascenso
01:11:24 Mauricio Gomez Quijada: A ver, y que quiero contar algo.
01:11:26 Cristian Murua: O sea no
01:11:30 Mauricio Gomez Quijada: Él no, no se trata el hecho de que tú no te laves las manos te va a hacer crecer. Ya, eso es importantísimo el hecho que yo venga y diga, sabes que fallé en esto. Y lo asumo y entendí, qué es lo que me faltó para hacerlo bien.
01:11:49 Mauricio Gomez Quijada: Te fijas eso te va a permitir crecer y te va a permitir hacer escenario. Yo jamás digo que te laves las manos lo que sí digo es Esperaban que yo lo hiciera a la primera, o sea, en una construcción nadie entra a trabajar y te lo digo que trabaje con mi juventud alguna vez en construcción nadie en la construcción entra y Espera que el que entró el primer día como ayudante cierto como Peón esté levantando te una muralla.
01:12:19 Mauricio Gomez Quijada: En la construcción no se te ocurriría a veces en desarrollo, se nos ocurre, no? Que entre No si como hace súper simple en construcción siendo más básicos que nosotros más rudimentarios que nosotros son más inteligentes. Dice cómo este tipo que es peor, acaba de llegar, va a levantar una muralla eres tonto. Te fijas Y si le llega a armar y se le cae. Nadie va a venir a decir Oye quedo la escoba por qué? Porque el tipo no supa armar bien la muralla, porque a esta altura ya lleva ahí y aunque el Peón lleve un año sigue siendo peor.
01:12:50 Mauricio Gomez Quijada: Entonces tampoco nadie lo va a mandar a hacer la muralla y si lo mandan a hacer y se le cae tampoco le van a echar la culpa. A eso es lo que voy, te fijas a lo mejor el viene y dice Oye me dieron la oportunidad de levantar una muralla y empiezo a levantar, dice cae. El tipo tiene que dormir en la noche tranquilamente reparar y llegar al otro día saben que fallé.
01:13:15 Mauricio Gomez Quijada: Tendría que haber puesto aquí un par de apuntes aquí dos puntales y con eso yo y tiro la Liana y voy manteniendo la rectitud Así se construye la moneda ahora trata de levantar la mano Es que la mezcla le hice mal Es que esto es todo Otro Bueno después que se te hayan caído tres cuatro murallas. Cierto, vas a levantarla bien Ahora que saben levantarla bien? Es menos probable que falles.
01:13:44 Mauricio Gomez Quijada: Si te falla ahí si fallas ahí, Date una vueltita más dije p*** eso soy tonto, me he fijado en esto ya Pero bueno ya se cayó la muralla ahora la levanto. Te invito a que eso sea, no, a que te laves las manos, no? A que no quieras crecer muy por el contrario el tema es que no te Auto tortures. Que no te exijas más allá. En responsabilidad a la que te corresponde.
01:14:15 Mauricio Gomez Quijada: Es como ahora, tú ves Toby está siendo prácticamente mi trabajo. Yo lo tengo haciendo mi trabajo a todo. Está liderando el equipo y yo estoy feliz porque estoy haciendo otras cosas y porque estoy probando la teoría de que Toby tenía muchas habilidades en eso. Si Toby falla quién falló? Yo yo soy el responsable, yo soy la cantabibility.
01:14:40 Cristian Murua: vos
01:14:44 Mauricio Gomez Quijada: Lo que yo espero es que Toby venga y se vaya muy tranquilo a su casa descanse Bueno ya está en su casa, pero teóricamente se vaya a su casa Descanse y mañana me diga, sabes que Mauricio yo creo que en estas situaciones Hay que hacerlo de esta otra forma. Vamos Totalmente de acuerdo y si no se le ocurrió yo mañana lo tomaré y le diré Toby Qué crees tú.
01:15:10 Mauricio Gomez Quijada: No fallamos en esto y fallamos en esto otro listo ahora démosle para allá. Me fijo y yo pongo la cara porque yo fallé. Eso es lo que tú quieras, que quiero que tú entiendas, quiero que mejores. Quiero que aprendas. Quiero que te perfecciones en todo lo que te falta lo más posible enfocándonos en el señoríority y que te dejes de torturar porque en tu señor y ti no tienes que torturarte guarda toda esa energía para cuando tengas el señor y de que lo vas a tener suficiente para hacer el responsable de las decisiones.
01:15:47 Mauricio Gomez Quijada: Ahí sí, castígate todo lo que tú quieras y hasta por ahí, dale más constructivo. ver cómo mejorar como equipo O sea, voy a fallar, se lo dije farid. Estoy, estoy cuidando todo lo posible para que no, pero como líder voy a fallar en algún instante. Por ahora todos Pueden decir hoy que bueno yo Mauricio puros aportes buenos puros goles de media cancha. perfecto, todo lo que ustedes quieran, pero yo no soy tan bueno como ustedes creen y cuando falle no voy a ser tan malo como ustedes creen tampoco sigo siendo el mismo Mauricio que tome una decisión equivocada, que debían lo pensado más que O inclusive hice todo lo posible te lo digo porque te va a pasar en su instante y eso es todo lo posible y la suerte, no me tocó, Yo pensé que iba a pasar esto pensé que iba a pasar esto otro tomamos todos los recuerdos y fallamos.
01:16:52 Mauricio Gomez Quijada: Ahora aprendimos lección aprendida. Veamos como lo mejoramos Cómo evitamos o controlamos que nos vuelva a pasar y seguimos adelante. No no es más que eso Cris la vida te depara y te va a deparar cosas más complicadas que esa. Y que son realmente importantes. Así que el trabajo no, no lo merece Hay que hacerlo lo mejor posible, hay que crecer por por uno por satisfacción por por orgullo, propio te fijas.
01:17:27 Mauricio Gomez Quijada: Pero no esas cosas una las hace con la familia con los hijos con la pareja con quien sea. Pero no con no con el trabajo, escucha un viejo que ya pasó los 50 años. que sabe lo que te dicen no lo tomes ni siquiera como consejo Escúchalo como un un viejo que se sentó en el banco al lado tuyo en la plaza y se puso a hablarte mientras te tiraba amiga, Las Palomas Así que te pido tranquilidad para mí ideal que te mantengas el tiempo posible como te digo si en algún instante aparece algo mejor y te vas yo lo voy a lamentar porque te tengo harto cariño, igual que el resto del equipo.
01:18:11 Mauricio Gomez Quijada: Y sé que podemos lograr algo bueno, pero lo voy a entender absolutamente, sino está mi compromiso absoluto de trabajar a full. Para mejorar, okay, Y tal vez en una de esas no lo sé. Buscamos un enfoque combinado si no tengo mucha más gente De mentoring probablemente el mentoring inicial lo haga yo y las exigencias más técnicas las haga Toby y así el día a día lo haces conmigo.
01:18:45 Mauricio Gomez Quijada: De crecimiento y a Toby lo llevamos netamente cuando necesitemos ver algo puntual de Sign Ups o decisiones, así no sé, hacemos un mentor incompatido también está la opción, me vas a tener que bancar a misia.
01:19:03 Cristian Murua: Gracias, me decís que la voy a pasar peor que con todo y bueno no, pero pero van guango Incluso si tampoco tipo te hablás loco, si Messi Che es obvio todo y no pasa nada, simplemente quería quería ver si era la sensación mía nada más sobre que bueno nada. vacaciones
01:19:24 Mauricio Gomez Quijada: no, es que en realidad Mira lo que pasa es Cuesta mucho a ver en arte de se dice una frase muy típica se dice. Aprende la técnica domina la técnica olvida la técnica. Eso cuando tú estudias arte a cualquier nivel siempre te dicen eso esos son los pasos para la maestría. Yo creo que en el en el uso humano pasa lo mismo obviamente te sientes Te puedes sentir afectado porque toto es una persona como Joel joven. Que están en pleno crecimiento técnico entonces.
01:20:06 Mauricio Gomez Quijada: Para ellos todo es obvio, lo que no es obvio, se se gastan una noche estudiándolo y llegan al otro día con cosas están en ese crecimiento. les cuesta más acompañar a Los Juniors porque tú para acompañar a un Junior o alguien más con menos expertiz que tú Junior me refiero a cualquier nivel puede ser líder Junior y eso no significa que sea Junior tienes un conocimiento lo que pasa es que como líder eres Junior Entonces tienes que entender que no todas las personas actúan como tú.
01:20:42 Mauricio Gomez Quijada: Y eso cuesta a veces sacarse Entonces cuando tú estás explicando y eres joven y eres impetuoso y eres talentoso como Toby Dices, pero esto yo lo entendería en un día te fijas.
01:20:54 Cristian Murua: claro, es como aprendió Java en una semana y me decía nada, es una boludez y es como que
01:21:00 Mauricio Gomez Quijada: Claro pero imagínate Yo trabajé en Java he trabajado diez años doce años en Java trabajé también en Mike de Microsoft Yo estoy, seguro que yo tomo a todo bien Java y y lo hago pasar un muy mal rato. Porque si él me puede decir yo aprendí y dije Ah bueno aprendiste listo soluciona esto haz esto y ocupa esta librería pero no ocupes esta otra y solucioname esto.
01:21:26 Mauricio Gomez Quijada: Y yo estoy seguro que lo voy a hacer pasar mal rato. Entonces por qué? Porque uno Tiene ciertas sensaciones de repente digo el tiempo te van enseñando que no, que no es tan así, te fijas y él tiene todo ese ímpetu, entonces sigue evaluando a los demás de acuerdo a su a su punto de vista Por su propia realidad no? De hecho yo siempre le digo probablemente tú seas mejor técnicamente que yo. Ya, pero sí te recuerdo que el Brasil campeón del año 1980 hay algo. Todos eran más jóvenes excepto hocicos que era el central de esa selección.
01:22:13 Mauricio Gomez Quijada: Y salieron campeones, gracias a sico, Porque si bien es cierto, no corría no era el más joven era el más experimentado y el más tranquilo. Pues él sabía dónde poner el pase siempre, entonces no todo es fuerza rapidez y energía a veces hay que parar aplicar un poco de experiencia. Y manejo de de circunstancias y ahí es donde yo apoyo no es porque yo sea una lombrina técnica Porque sinceramente. Creo que los chicos pueden ser mejores el hombre de las técnicas que yo.
01:22:44 Mauricio Gomez Quijada: Así que en una de esas hacemos un combinado te parece
01:22:49 Cristian Murua: Me parece me parece correcto.
01:22:54 Mauricio Gomez Quijada: Dame un segundito que me está escribiendo mi novia. Y esa sí la tengo que responder porque Y te digo que estés tranquilo, luego revisaremos tu evaluación. Me gustaría que hicieras eso que vienes para atrás que te los dos aspectos ni desde como Junior toma el vertical de Junior Y midete y luego toma el vertical femicinio y mídete.
01:23:30 Cristian Murua: Pero por ejemplo. Qué papel, juega la la frecuencia, digamos, por ejemplo, lo primero que se viene a la cabeza a mí con eso es el tema de los comments de la PR de las perras todos. Si vienen signos no comentó una PR que entré. Alguna que otra en Uma he comentado. Entonces como que no sé si decir bueno, eso no está check o está check? Estoy haciendo un poco el fantasma de decir Bueno alguien con más Que obviamente igual entiendo lo que decís vos de las perras de cuestionar de preguntar por curiosidad y demás en signos, por ejemplo, no lo estoy haciendo eso.
01:24:19 Mauricio Gomez Quijada: Te queda tarea Allí estoy al debe, te fijas estoy al debe Por qué, Porque la
01:24:20 Cristian Murua: Igual me llevo para armarme el documento de Word con
01:24:25 Mauricio Gomez Quijada: PR es un indicador, es algo que se llama ejemplo positivo de comportamiento. Pero lo que yo busco es el trasfondo lo que yo busco no es que Comenten las PR que sí lo pueden hacer si quieren más de lo mismo te fijas lo que yo busco Es que participen en el debate técnico. Eso es desde su señorita, uno no tiene porque a veces la duda. Es una discusión técnica. Te fijas alguien puede venir y decir justamente.
01:24:56 Mauricio Gomez Quijada: lo que te comentaba de Joel Con el Data o lo de Toby con que Florence se complicó solo Eso genera una discusión Y esa discusión a veces cuando hay múltiples señorita y dan otros puntos de vista puede venir alguien y ponte que hubiera un otro integrante sea más Juanito Pérez y dijera No sé no Esto está malo porque jamás se debe hacer. Te diga y yo vengo y digo ojo, porque eso depende de muchos factores, yo he tenido la experiencia que hay que hacerlo.
01:25:33 Mauricio Gomez Quijada: y que ciertos lenguajes te lo permiten, te fijas, o sea Qué cosa te puedo decir orientación objeto. Esto es algo, que yo lo escuché mucho hoy día lo están corrigiendo. Gracias a la Inteligencia artificial. Yo creo porque pero en muchos textos inclusive había un error Decía si tú quieres aprender orientación a objetos pura. apréndese Y aprende o se más más y con eso aprenderás que no hay un lenguaje menos orientado a objetos que se Sepan más un poco más, pero sé nunca fue orientado objeto.
01:26:12 Mauricio Gomez Quijada: Sí Y por qué no porque tiene herencia múltiple en serio, puedo hacer herencia múltiple Y eso es lo primero que te dice orientación objeto que no puedes tener. Yo he venido solamente de una clase, no de múltiples clases y sé yo podré dar de múltiples clases. Y porque a través del manejo de punteros yo puedo romper el encapsulamiento. Por lo tanto se no es un lenguaje orientado objeto y nunca lo fue, sé más nunca fue creado para ser orientado a objetos, quieres aprender un lenguaje orientado a objetos aprende Eiffel Eiffel es un lenguaje orientado a objeto? Sí, hice más más nunca sigo pensados orientados Entonces te fijas hay mucha discusión eso Y a mí me ha tocado estar en discusión donde viene y dice oye.
01:27:00 Mauricio Gomez Quijada: Entonces qué Tengo que aprender un Junior pregunta y si yo quiero aprender orientación objeto, qué qué quiero hacer? Y el cine viene y te dice cepo, sí sé más más. Y viene Mauricio o cualquier otro dice, no? Y te explico todo esto y te digo Eiffel eifel, es orientado. Nadie conoce ese lenguaje Pero existe y es orientado objeto Okay entonces una pregunta ayuda a la discusión técnica No todo es resolutivo Entonces qué es lo que busco yo con las PR digo en el fondo al punto de Aporta a la discusión técnica cierto, qué busco yo en el semi senior que pueda desarrollar feature con complejidad media con la mínima supervisión y obviamente mantenga la calidad se fija Cuando digo realizar reviews efectivos para compañeros, Fíjate que el trasfondo de eso es aportar a la discusión técnica.
01:28:05 Mauricio Gomez Quijada: Porque ya se los dije que los los review bueno, tiene otro fin Pero el código review. no es encontrar errores en el código el de encontrar errores es un resultado del proceso del Review tiene como fin unificar criterios técnicos. Por qué lo hicimos así? Ah bueno anda a ver los los que se hacen en las cosas open. Si participas Y ves los revivas Oye por qué lo hiciste así porque en el documento de implementación que se hizo para este proyecto indica que si yo quiero hacer esto Debo hacerlo bajo esta estructura.
01:28:52 Mauricio Gomez Quijada: Ah ya a ver Ah no, si tienes razón chuta me equivoqué yo Entonces yo lo estoy haciendo mal lo hubiera corregir. Ese es el fin O sea no es el hecho que participen entonces ve Cuál es el objetivo de fondo te fijas desarrollar features con complejidad media con
01:29:08 Mauricio Gomez Quijada: mínimas supervisión quiere decir que sean autónomos. Que puedan tomar decisiones, te fijas que va, no que no vayan a fallar. Ahí dice no dice desarrollar Fisher de complejidad media con mínima supervisión y que nos fallen y no tengan ningún error y se saquen puros diez y reciban felicitaciones en el Canal de reconocimiento no dice eso. Dice desarrollar dichos media con víctimas. O sea, Obviamente si yo entro y soy Junior tengo que preguntar todo.
01:29:43 Mauricio Gomez Quijada: Y eso te indica que eres Junior o sea Oye cómo se hace un Hoyos y no me funciona Ah que tiene que agregarle este middleware aquí está anotator. te fijas Ah ya y el anotator siempre tiene que ir con la primera en mayúscula o eso es un Junior Un femicidio ya no, él sabe que va con la notator que lleva esto yo no tengo que supervisarte, tú haces el ciclo y haces el PR independiente que en el PR probablemente yo te haga con review y te revise cosas.
01:30:15 Mauricio Gomez Quijada: Pero ya estás ahí ahora si llegas a excelencia, ya estás brindando casi como un como un senior. eres autónomo con ninguna supervisión lo haces y lo hacen bien Eso es un Silvio por eso todo y para mí él hace las cosas de código Las hace bien cero supervisión, Yo no necesito supervisarlo, le digo ABC él lo hace y muy rara vez se equivoca tiene y cuando se equivoca se da cuenta solo y dice voy a corregir porque me mandé aquí un rato en código de cine.
01:30:58 Mauricio Gomez Quijada: Te fijas, no sé qué Busca cada una de estas cositas en el fondo. Eso es lo que te tienes que que preguntar colaborar con área de producto u ex para alinear, las decisiones técnicas con las necesidades del usuario final no quiere decir que tú tengas que tener reuniones constantemente con producto y ux, que no es necesaria y yo digo es ten en cuenta las necesidades del usuario. Estás pensando en el usuario como lo hiciste cuando fuiste fronen y tomabas decisiones Oye decidí esto porque le va a quedar más claro al usuario.
01:31:32 Mauricio Gomez Quijada: Te fijas eso es una decisión que cumple con eso y si lo estás haciendo constantemente. cumples Te fijas Busca que hay detrás de cada cosa y si no tienes Claro que es lo que hay detrás me preguntas a mí. Claro porque es una de esas tú llega y dice mira en realidad yo en Junior
01:31:56 Cristian Murua: Con el análisis de los dos señores y si fue. Y por hoy completando en base a lo que yo creo.
01:32:09 Mauricio Gomez Quijada: inclusive en Junior he detectado que tengo unas falencias me gustaría también trabajar en esa y luego hacerla crecer para nivel senior. Y puede ser, te fijas. no No creas que técnicamente. Técnicamente yo debía ser un líder principal Por eso soy más que senior de como liderando equipo. Pero si tú me haces pasar por un mapa de Test principal yo cumpliré el 80% de las cosas Las otras 20% de seguro me faltan Bueno cómo Y si eres principal, Sí claro. Pero yo en mi tarea del día a día no las necesito, ni las he necesitado.
01:32:58 Mauricio Gomez Quijada: Te fijas el 80% era el 80% principal que se requiere para ese rol cierto y las tengo bien afinadas. entonces a ti te va a pasar lo mismo probablemente pases por Junior y digas Oh que deprimente no llego al 100% de Junior Entonces cómo estoy pensando en Como estoy pensando en semicinio, sí va a pasar. Y luego va a llegar al setenta ochenta por ciento, probablemente, Ojalá cumplamos todas Pero muy difícil.
01:33:28 Mauricio Gomez Quijada: Te fijas lo importante son esas tienes nivel técnico, si participas en el nivel
01:33:35 Mauricio Gomez Quijada: técnico, sí. Te fijas, Dame un segundito. Y eso en el fondo es eso te fijas. o sea no Ya me estaban retando Ya viste entonces. no, no te no te caliente en la cabeza eso en el fondo tdlr no te calientes la cabeza, me enganché que una uña me Ve, mídete. Valúate y autoevaluación honestamente y luego esa misma matriz la usamos. Y vamos haciendo un acuerdo y decimos tú me puedes decir. No sé, yo no cumplo. No sé.
01:34:45 Mauricio Gomez Quijada: Adaptarme rápidamente a los cambios de prioridades tecnologías o procesos dentro del equipo. Tú me puedes decir? Yo creo que no cumplo en eso. Y yo te voy a decir, yo tengo dos o tres claros ejemplos que sí cumples. te fijas entonces para mí sí, cumples y Ahí vamos viendo un un matter, te fijas y ahí probablemente en una tú dirás que cumples y yo te diré que no cumples viceversa otra vez estaremos de acuerdo que no cumples y otra vez estaremos de acuerdo que Y al final Sacaremos un una Estrellita y veremos cómo es tu situación te fijas.
01:35:21 Mauricio Gomez Quijada: entonces, pero fíjate que eso es una Es una vertical, no más, o sea, técnicamente es una vertical. Y hay cosas que son más importantes dentro de la vertical. de responsabilidades las importantes son que desarrolles que seas autónomo y tengas un buen nivel técnico Eso es lo más importante técnicamente el reto es
01:35:49 Mauricio Gomez Quijada: Todo todo anexo, ya después tienes cosas más de de soft skill. Que son las que hay que ir trabajando a veces más que es, pero yo he notado un crecimiento. Ah yo te yo te voy a ser súper honesto. Yo sé. Porque no soy viejo O sea no soy tonto y soy viejo. De que tú tenías un conflicto en las presentaciones en general se te nota, no, no era de tu agrado estar en reuniones, te costaba. Te fijas, etcétera.
01:36:26 Mauricio Gomez Quijada: Desde que yo llegué ahora en estos tres meses, has crecido una enormidad. Yo te veo en las reuniones primero con la cámara prendida que aporta harto y te veo con una postura en la cámara. De seguridad y eso se nota, te lo quiero hacer Te lo quiero.
01:36:45 Cristian Murua: Me retó todo, pero dijo prendé la cámara que suma puntos.
01:36:47 Mauricio Gomez Quijada: Ah sí Pero pero ojo.
01:36:54 Cristian Murua: Pero yo tipo estoy me hice porque yo sé que estás, pero con la cámara apagada estoy mirando un icono.
01:36:59 Mauricio Gomez Quijada: Sí claro, y cómo se llama esto, pero él te lo dijo y está bien. Pero tú lo asumiste porque yo puedo estar así también. Yo puedo tener una cámara y puedo estar así. Y estoy con la cámara prendida, te fijas, no? Tú cuando estás en las reuniones, estás así. Tiene llenas el cuadro que es importante, eso es una recomendación siempre cuando tú estés en cámara llena el cuadro.
01:37:28 Mauricio Gomez Quijada: Ya sigue lleguen bien al tope te fijas que te veas grande en el tema y estás llenando el cuadro y estás seguro, estás así. Serio concentrado, seguro eso, eso es mérito tuyo no es mérito de toto, ya no le quiero quitar el mérito a Toto pero de decírtelo Pero tú dijiste. Cierto, sí tiene razón Y si voy a estar, voy a estar bien esa actitud es la que yo quiero en código. Esa es la actitud Esto está bien, me tomo este este esta mejora.
01:38:04 Mauricio Gomez Quijada: bueno Fuimos al partido de la pool recuest y salí goleado así en cero prácticamente se gastaron más tiempo escribiendo que es lo que yo hice en líneas de código. Bueno, pasa a veces así a quien no le ha pasado a mí también me ha pasado. Y lo empiezo a analizar, yo se la hice a él te lo comento aquí entre nosotros en seguridad. Cuando él vio la primera vez test y me mandó los test.
01:38:36 Mauricio Gomez Quijada: Para que yo lo revisara Yo le hice un testamento así los tres no se hacen así no se hacen Acá no tienes que tener cuidado con esto tiene que tener cuidado con esto ojo aquí esto es un error, si lo hace de esta forma no lo puedes tomar de esta otra. En cero y él lo tomó y lo estudió y dije. Pídele el curso a Farid, el curso que tienen de typescript y perdón de testing con javascript, pídelo porque ahí el chico lo explica bastante bien Yo conozco ese curso y bueno Y él lo pidió lo estuvo revisando y ahí quedó mucho más claro con Cómo tenía que hacer las cosas.
01:39:16 Mauricio Gomez Quijada: entonces A él le encantó que yo lo goleara porque le dio una opción de aprender. Algunos somos más lentos, aunque tú no te creas yo soy de lento aprendizaje, No soy como todo. Yo no aprendo rápido como Toby necesito madurar las cosas y procesarlas. Algunos somos más lentos y nos golean más veces. Pero si si en la primera te golearon cien cero trata que en la segunda sea noventa y nueve cero. Si ya te golearon te metieron un gol menos, vamos a vamos mejorando la defensa.
01:39:54 Mauricio Gomez Quijada: Desvía Entonces si ya te vuelven a golear sincero, tú dices Bueno aquí, Qué pasa, estoy teniendo errores de regresión.
01:40:02 Cristian Murua: Buenas tardes, avanzando directamente
01:40:04 Mauricio Gomez Quijada: Claro, entonces a ver, cambiamos la estrategia o no sé, sabe que le voy a pedir a Mauricio si tiene quince minutos para que revisemos que está pasando aquí, porque esto no puede ser. Ah pero es que este es nueva es un tema nuevo que estás aprendiendo está bien partes perdiéndose en cero y ya en la próxima que hagas lo mismo te tendrían que golear 99 perfecto Esto es así no tiene otra vuelta Cris Ya, pero que te lo tomes con calma en la parte técnica. Y te auto evalúes, te des el tiempo.
01:40:38 Mauricio Gomez Quijada: y luego nosotros nos juntamos cuando yo te dé el feedback mío, Ojalá Estén lo listo para cuando yo empiece los feedback que probablemente en la próxima semana. Voy a empezar con todos ustedes a revisar los feedback del q ahí, si lo tienes listo, aprovechamos hacemos una sesión intensiva y revisamos todos los feedback, te parece?
01:41:01 Cristian Murua: Sí yo el coso de dinero ya lo complete la evaluación me falta armar.
01:41:04 Mauricio Gomez Quijada: Espero que no haya sido extremadamente autocrítico
01:41:06 Cristian Murua: el el coso del carrier Paz Un poco y un poco fue como que paraba un segundo y decía bueno para tanto no. Y viceversa, o sea, ambos lados, me puse me agregué puntos y me quité puntos después de pensarlo un poco. Pero tampoco quiero que sea un punto medio entre objetividad y también. como ni muy muy ni tan tan
01:41:46 Mauricio Gomez Quijada: Te fijas esto te debiese haber ayudado a a orientarte, Yo estoy de acuerdo, estoy viéndolo, yo puedo ver más o menos la parte principal de lo que te evaluaste, no todo, pero puedo ver algunas cosas. espero que te hayas analizado como como Junior Ya no, como te evaluaste Cómo semicinio ya.
01:42:04 Cristian Murua: no
01:42:09 Mauricio Gomez Quijada: Ya, yo te tengo que evaluar como Junior porque me da mayor posibilidad de poder decir de repente sobre cumplen algunas cosas porque si te evalúo como simio probablemente, quedes mal. Que es más bajo, entonces va a quedar la impresión de que como Junior no estás cumpliendo.
01:42:26 Cristian Murua: claro
01:42:28 Mauricio Gomez Quijada: Entonces yo eso lo corrijo No te preocupes, yo lo reviso y lo corrijo. Pero ahí también te dan ciertos parámetros si tú te fijas. Tú dices como Junior comprendes los requerimientos básicos escucha activamente y cumple lo solicitado sobre cumple. Para mí eso lo sobrecumple porque no solamente comprende los requerimientos básicos, ni escucha activamente ni cumple los solicitados, sino que generalmente interpretas cosas adicionales y lo lleva un poco más allá cuando está dentro de tu conocimiento ahora como analista cumplirías.
01:43:06 Mauricio Gomez Quijada: Yo te diría cumple parcialmente Como senior porque interpretan necesidad implícitas muchas veces sí, En mi punto de vista. Proponen mejoras alineadas al cliente usuario muchas veces lo has hecho sobre todo cuando has estado en en marchando con él donde te has sentido más más cómodo porque lo entiendes más rápido. colaboras con solución a medida todavía no porque no te ha tocado implementar la parte del backen y sentirte cómodo en donde sí podrías tener más juego en eso porque fronner no tiene mucho juego en eso Pero backenzi te fijas entonces empatía.
01:43:45 Mauricio Gomez Quijada: Me parece sobre cumple para escuchar con atención para ser respetuoso y receptivo.
01:43:51 Cristian Murua: claro
01:43:52 Mauricio Gomez Quijada: Yo creo que también reconoce distintos puntos de vista que es lo que espera uno deducir de su semicidio. Ajustas tu estilo según la persona y el contexto No lo sé porque yo te veo solamente un contexto, que es conmigo. Te fijas que fomenta relaciones positivas, sí. Pero yo mejoraría más aquí todo lo que tiene que ver con la comunicación y con la empatía de acuerdo a la persona en el contexto.
01:44:21 Mauricio Gomez Quijada: Te fijas yo te lo reforzaría más, o sea cómo te lo reforzaría que tuvieras que lidiar con más personas. Te fijas y yo poder preguntarle a ellos después Oye cómo estuvo Cris en esto cómo estuvo y con eso yo sacar mi conclusión no te expuesto a eso, así que no lo sé.
01:44:38 Cristian Murua: igual tipo muy por debajo de la mesa ocurre mal Que mal, o sea Trato, tengo con todos en el equipo incluso digo cuando está laburando en el Front como que está bastante. cercano a lo que era a Yani a Flor de como que Sí eso ya tipo ahora que lo pienso como que no sé. quizás si es positivo el puntaje no me acuerdo que voté igual no me acuerdo
01:45:02 Mauricio Gomez Quijada: Te fijas.
01:45:04 Cristian Murua: que que puse pero creo que puse cuatro de cinco una cosa así porque
01:45:07 Mauricio Gomez Quijada: Por eso, por eso es muy importante que analicen las cosas y las re pienses No
01:45:13 Mauricio Gomez Quijada: desde el punto de vista crítico. crítico en el sentido de de destructivo sino que desde el punto de vista de situaciones que buscan preguntarme con esto te fijas Se yo agilidad y adaptabilidad un Junior se adapta a cambios con guía, acepta feedbay y ajusta su accionar aprende de experiencias nuevas, Sí tal vez lo único que yo te mejoraría ahí es él aprende de experiencias nuevas. Yo creo que ahí tienes que trabajar en lo que hemos dicho.
01:45:46 Mauricio Gomez Quijada: de decir Me importa que falle da lo mismo voy a fallar con personalidad aquí en Chile tenemos un dicho. Entre mi gente digo si la voy a c****, la voy a c**** hasta acá arriba me voy a meter en la m***** hasta el cogote de eso nada de pisar la caquita así como por el borde No si me voy a meter en la m***** me meto hasta acá.
01:46:09 Mauricio Gomez Quijada: Te fijas Entonces sí porque sé que no, que la voy a c**** un poquito, no, si la voy a c**** c***** con personalidad, o sea. Entonces aquí lo mismo voy a ir y voy a ir de frente, ya lo revisé según yo. Ya Di lo que tenía que dar es como en las pruebas en la universidad En la facu, entonces diga en la facultad doy y después te llega un uno chucha Ya di no lo haría hasta el final yo era de eso de cuando llegaba.
01:46:42 Mauricio Gomez Quijada: Yo tenía compañeros que se quedaban las seis horas como si iba a venir un rayo de luz divino y los iba a tocar en el último minuto y van a responder. No sabía nada, pero amasaban la hoja las seis horas, yo no llegaba y venía nombre, me la sé, no me la sé, no me la sé, no esta me la sé, la voy a poner, le voy a dar otra vuelta a ver.
01:47:09 Mauricio Gomez Quijada: tengo capacidad de hacer algo en esto no tengo idea lo que me Estás me acuerdo ni me acuerdo de eso mi nombre. Hasta luego como ya terminó es lo que sea, entonces un código es lo mismo. Tú vas entregas hoy, sabes que tengo dudas más menos lo voy a hacer así. Te parece bien antes de entregarlo, No yo corregiría esto esto y esto perfecto lo corrijo lo envío. Oye pero habría que corregir Esto me lo traigo y lo corrijo. Sí, tiene razón, hay que tener cuidado en eso este nombre. Ay no me queda tan Claro porque Toby me propuso cambiarle el nombre.
01:47:54 Mauricio Gomez Quijada: será mejor o porque a él se le ocurrió a ver lo voy a preguntar a Mauricio Entre a y b, cuál te inca más vamos tenía razón es mejor esto eso es lo que yo te mejoraría de aprende de experiencias nuevas. Ya de ser más sistemático en eso de no tenerle miedo a que te goleen, Dale nomás. qué es lo que busca un poco ese esa vertical en Cecilio Se adapta con autonomía cambios de contexto. Te falta todavía base técnica para eso propone soluciones anti imprevistos. Yo no te he visto en esa postura, pero me gustaría verte Me parece interesante.
01:48:40 Cristian Murua: Y eso es más de budeo que otra cosa, no?
01:48:45 Mauricio Gomez Quijada: Agregar sí Y cómo puede solventar si yo te cambio el contexto es decir. Cómo reaccionas tú o cómo te adaptas? Si yo te dije tienes que hacer una escalera aquí tienes madera, vas a tener madera y vas a tener clavos. Y luego yo vengo y te entrego, no sé unas ramas y pegamento. Te fijas Eso es en el fondo cuando tú haces bien y le dices al semi senior porque el semicinio no es cien por ciento autónomo. O sea, es mayormente.
01:49:24 Mauricio Gomez Quijada: Vienes y le dice, oye, sabes qué lo puedes hacer? De esta forma, pero luego a veces uno se da cuenta que el Kart apunta para otro lado. Eres capaz de darte cuenta de que el Kart está apuntando para otro lado y que lo tienes que solucionar con otra técnica, no con la que yo te dije eso es un poco, para dónde va esa ese tema. No se da mucho ahora se va a empezar a dar más porque estamos con clientes.
01:49:52 Mauricio Gomez Quijada: Antes tuvimos muy relajados, entonces hacíamos lo que queríamos en evaluación, No crecíamos mucho tampoco, pero con clientes sí se notan más este tipo de cosas. Apoya a otros en su adaptación, yo creo que sí, que tú tienes un muy buen enganche con la gente flor en él. la gente tiene en estima cada vez que pueden te recuperan así que significa decirte que eres un aporte por lo tanto apoyas a los otros en la adaptación tu conocimiento de Bach también los apoya Así que bien Data centric, ahí no hay mucho porque Data centric, no somos un equipo que haya estado trabajando Data céntrico para mí todos cumplen puntos porque interpreta a datos básicos con guía Utiliza herramientas para obtener información relevante un analista Junior no se me ocurre nada en este que hayamos aplicado eso así que para mí cumple un semi senior analiza datos con criterio relaciona datos con objetivos del negocio apoya a decisiones basadas en evidencia Papitos cumplen en este instante porque no estamos Data céntricos, ya vamos a estar datacéntricos.
01:51:12 Mauricio Gomez Quijada: Nos divertimos a ver autonomía de impacto. cumple con tareas con supervisión si aprendes a priorizar sí aporta valor en tu rol sí cumple en en Junior gestiona trabajo con autonomía prioriza en función del impacto ASUME responsabilidad por los resultados aquí Aquí Cris Tenemos que trabajar en este q3 q cuatro, pero no tú todos. Esto es una tarea transversal que cada uno se haga cargo de las tareas, porque hasta ahora están llegando desde arriba al igual que en un principio era farid.
01:51:52 Mauricio Gomez Quijada: Ahora es Toby con Santi tenía mente soy yo también, pero ni trabajo en esas cosas obviamente no me voy a poner medallas que no tengo. Y la idea es que ustedes las tomen y las destrocen, o sea, viene Toby dice yo como tal persona quiero hacer esto y puedo hacer esto estimación veinticuatro horas a ver para hacer esto tengo que hacer un formulario. Me toma dos horas tarea uno formulario dos horas para hacer esto debe hacer esto otro Son seis horas, estos otros son diez horas, no, no me da con veinticuatro horas, tiene que ser 32 y aquí está el desglose de tareas y esto es lo que yo me demoro en cada uno, eso tienes que cumplirlos todos.
01:52:32 Mauricio Gomez Quijada: Y eso es lo que busca esa tarea gestionar trabajo con autonomía priorizar en función del impacto y asumir responsabilidades por los resultados no llegamos al Sprint yo fallé al estimar, pero no se preocupen, me ajustamos en el próximo hago carry y me me comprometo y ajusto los tiempos punto Así que tú tienes esa mentalidad lo único que tiene que hacer ahora más más activa que reactiva y punto Qué más tenemos, colaboración y comunicación? comparto información Clara y oportuna colabora cuando se le solicita Claramente para mí sobrecumples.
01:53:17 Mauricio Gomez Quijada: Como semicidio que dice facilita el trabajo en equipo comunica con Claridad y asertividad y escucha activamente Aquí vamos a trabajar harto. Quiero trabajar harto contigo en lo que son presentaciones entregas de cosas, Ah quiero hacerte que comuniques. Al equipo en general cosa chica pequeña no te voy a mandar. A los leones ya son cosas chicas, esta semana me gustaría que explicaras este temita o es que que vieras en el retrospectivo, viéramos tal cosa.
01:53:55 Mauricio Gomez Quijada: O algo que voy a hacer con todos también es que todos participen en el retrospectivo porque hasta ahora yo soy el que los conduzco las retrospectivas de Sprint Y ustedes dan su feedback, no de ahora en adelante, quiero que ustedes vayan de a poco también tomando el control de la retrospectiva. En alguna medida Se los voy a avisar anticipadamente para que los preparen no los voy a lanzar. Ahí no soy de ese.
01:54:21 Mauricio Gomez Quijada: de ese estilo de de Liderazgo de de cómo se llama esto de Bueno, Me estás pidiendo que transfiera darme un segundito Así que eso yo lo mejoraría en en semicinio, verdad? No estoy hablando de Junior para mí en Junior cumple Prácticamente todo lo que dice acá. luego Luego necesitamos ver lo de nosotros lo que vimos en esta carrier paz, que es el que me interesa Esto es para recursos humanos para que recurso humano vea que según sus indicadores, tú estás bien? Está sobre Junior luego tenemos que ver que te falta para hacer si mi señor en el más ampliado que tenemos nosotros.
01:55:09 Mauricio Gomez Quijada: Innovación a ver participa en iniciativas aporta ideas frescas se muestran abierto al código para mí sobrecumples este como Junior este semestre ha tenido un par de ideas bien interesantes autónomas por ti Eso del firma de todas esas cosas fueron autónomas, Porque para mí, Por lo tanto como Juniors sobrecumples como semicinio desarrollas nuevas formas de hacer y habrán mejoras en procesos motivar a otros a pensar diferente Eso es lo que espero de ti en cúteres y q cuatro.
01:55:44 Mauricio Gomez Quijada: Quiero que lo concretemos de alguna forma, cómo motivamos a los demás a pensar diferente. Te fijas Como que generamos nuevas formas de hacer cosas porque ahora vamos a tener el espacio. Entonces lo tienes, pero hay que demostrarlo hay que ver que sea. tangible Así que como Junior sobrecumples como semicinio obtienes la capacidad de hacerlo pensamiento crítico cuestiones con respeto identifica errores básicos solicita guía para resolver problemas si identificas errores básicos también te he visto identificar cuestiones con respecto te falta cuestionar tienes demasiado respeto a la autoridad y al conocimiento y eso te lo tienes que sacar Un poco ya no farid, es mi jefe.
01:56:36 Mauricio Gomez Quijada: Es el top de Top yo lo admiro un una enormidad créeme y le tengo un respeto increíble, pero le cuestiono todo lo que puedo cuestionar. Porque ese es mi trabajo. No es un cuestionamiento a su capacidad es un punto de vista distinto. Así de simple eso te falta un poco cuestionar más. evaluar opciones con criterio detectar riesgos argumentar con datos No hemos tenido opción de eso pero tenemos que poner en el roadmap para ver cómo hacemos accionables para que puedas crecer en eso Habilidad técnica analista Junior se espera que este proceso te fijas espera que esté en proceso de aprendizaje técnico Dime lo que tú quieras, dime que tienes 20 años me da lo mismo.
01:57:25 Mauricio Gomez Quijada: La empresa no hizo el proceso para hacerte crecer de señority, eso no es responsabilidad 100% tuya la empresa también tiene una responsabilidad. Ya, así que comprendo los conceptos básicos requiere acompañamiento frecuente. ya no requiere tanto acompañamiento Ah yo creo que eso has
01:57:48 Cristian Murua: Aliño con todo y hasta te diría que las últimas tareas directamente esperaba el review nada más.
01:57:54 Mauricio Gomez Quijada: Así que es valorable que tenga iniciativa para mejorar y capacidad de aplicar lo aprendido yo creo que lo tienes para mí en esto habilidad técnica sobrecumples como Junior Como semi senior dice debería manejar con autonomía las herramientas y procesos principales de su rol. Lo manejas Puedes resolver problemas habituales sin asistencia y empezar a compartir conocimiento técnico con otro del equipo, no compartes conocimiento, por lo tanto cumple parcialmente ahí hay que trabajar. Te fijas.
01:58:26 Mauricio Gomez Quijada: me digas que
01:58:27 Cristian Murua: Igual, o sea que es proponer usar alguna librería es como un ejemplo.
01:58:33 Mauricio Gomez Quijada: Claro, o sea, por ejemplo a ver.
01:58:39 Cristian Murua: porque ya que
01:58:41 Mauricio Gomez Quijada: compartir conocimiento, por ejemplo cuando mica viene y dice Chicos, tuve este problema y probablemente este problema ustedes también les surja más adelante o si alguien toma esta misma funcionalidad más adelante Les explico que hice esto de esta forma. Lo que hizo Nico tenemos un problema en la recurrencia encontré una librería bastante buena que podríamos analizar de usar que nos quitaría problemas. Se fija Por qué Porque luego yo vengo y le hago la segunda pregunta, le hiciste npm audit Viste que no tuviera problemas de compatibilidad que se esté manteniendo Pero él compartió conocimiento. Se fija, yo hago mi tarea no quiere decir que la vayamos a usar.
01:59:29 Mauricio Gomez Quijada: Te fijas porque no es tarea de él decidir si vamos a usar esa herramienta, no? Pero él dice la acabo de ver la veo relativamente madura y mantenida. y creo que nos podría aportar en esto dejo el
01:59:44 Cristian Murua: En la misma que había sugerido yo y todo y mi hijo no va a ser lo negativo.
01:59:47 Mauricio Gomez Quijada: Te fijas Por qué Porque tienes que hacerlo en el grupo?
01:59:51 Cristian Murua: Claro, eso es lo que me está faltando hablar en el canal.
01:59:55 Mauricio Gomez Quijada: Habla en el canal porque los que tenemos que decidir es entre todos. Te fijas Como no lo hablaste en el canal porque Probablemente lo hubieras hablado en el canal y Nico hubiera dicho, yo también la estoy viendo, Qué te pareció por último hubieran podido intercambiar entre los dos opiniones.
02:00:11 Cristian Murua: No, no igual te hablo cuando estaba Armando la afip en lo hace sprints anteriores, te estoy diciendo tomarme la pintamiento recurrente.
02:00:17 Mauricio Gomez Quijada: pero todos tenemos de formas distintas de Y capaz que tú vas y se lo dijiste a todavía, no sé, estoy hablando de una tontera Bye y se lo dijiste. O sea, en otro contexto y él dijo No mejor, voy a preferir que Cris no se meta
02:00:34 Cristian Murua: No hace falta.
02:00:36 Mauricio Gomez Quijada: en esto que mejor lo hagan activo para que lo entienda. A lo mejor él estaba en otra, pero en el grupo en el grupo no porque en el grupo estamos todos entonces ahí hay distintas visiones en el peor de los casos, lo pone y Para que no se me mira, mira la técnica para que no se me olvide lo dejo aquí alguno de ustedes ha trabajado con esta librería. Yo así la veo al pasar y la veo bastante buena nos podría ahorrar algunos problemas. Lo dejo aquí, por si alguien quiere comentar algo.
02:01:08 Mauricio Gomez Quijada: No tomo decisión, no me quemo no digo, tenemos que hacer esto para que nadie se sienta ofendido ni nada, sino que Ah estoy aquí acabo de ver una librería buena la dejo aquí para que la comentemos si quieren Sí si no, no, pero portaste. Te fijas y quedó registro de eso. Entonces tienes que venderte Cris tienes que venderte. ya Y bien te fijas Lo bueno que tiene eso de estar viendo lo Dual como Junior me faltan algunas cosas a lo mejor en esto, pero sobre cumplo la mayoría.
02:01:50 Mauricio Gomez Quijada: Y luego lo aterrizamos al carrier paz, que es el que más me importa a mí porque es el que nos da como nosotros podemos trabajar para mejorar lo que falta. Porque esto es básico esto que manda hr es súper básico. No sirve para vender la pomada. Para generar ahí un buen Support una buena base, pero el que me interesa es el maper que hagamos de esta misma forma Junior y semi senior.
02:02:20 Mauricio Gomez Quijada: Porque te vas a encontrar que en Junior ya está sobre cumpliendo muchos pero en semicinio tienes gaps. Entonces quedas como lo que aquí le catalogan un semicinio menos o un Junior o más no alcanzas a hacer si mi simios. Pero ya no eres tan solo Junior Entonces qué te falta, Qué te falta para dar ese paso y ese es el Gap que tenemos que atacar. Ese esa es la estrategia.
02:02:48 Mauricio Gomez Quijada: Te te convencido, no?
02:02:51 Cristian Murua: Sí sí obviamente es como arracó la charla, digamos, básicamente sigue todo igual. Pero bueno nada
02:03:02 Mauricio Gomez Quijada: A veces quisiera que las cosas fueran.
02:03:04 Cristian Murua: No, no, no, no, ese A ver, no depende tampoco de vos es totalmente entendible Así que nada. Es poner de seguir poniendo de mi parte y bueno, ojalá que al fin de año ya se normalice todo.
02:03:17 Mauricio Gomez Quijada: Y ahí vamos, vamos viendo si las cosas en la medida que uno las hace bien y empieza a aprender empiezas a crecer. Y al menos después no está sufriendo tanto en nuestros aspectos y empiezas a encontrarle el lado. Bueno, ya tiene un lado positivo. toda esta parte de la parte social y todo lo demás que también te hace crecer así que Trabajemos En eso, yo voy a estar también revisando los feedback Y como te digo cuando los tenga claro.
02:03:55 Mauricio Gomez Quijada: Lo volvemos a conversar y tú haces en paralelo la tarea con el carro y el Paz te parece. algo más Cris
02:04:03 Cristian Murua: Nada más por el momento voy a poner a leer Data que me pasó Todavía así que
02:04:06 Mauricio Gomez Quijada: ánimo
02:04:09 Cristian Murua: bueno, me entretengo ahora a la tarde.
02:04:11 Mauricio Gomez Quijada: me parece un abrazo cualquier cosita al Canal
02:04:15 Cristian Murua: Dale genial, Mauri Muchas gracias
  