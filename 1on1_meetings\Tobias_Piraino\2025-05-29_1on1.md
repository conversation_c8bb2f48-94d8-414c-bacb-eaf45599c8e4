# 1:1 con <PERSON> - 29/05/2025

## Información básica
- **Miembro del equipo**: <PERSON>
- **<PERSON><PERSON>**: 29/05/2025
- **Duración**: 77 minutos (10:15 - 11:32)
- **Ciclo de revisión**: Regular
- **<PERSON><PERSON> clave**: Carrera, Liderazgo, Arquitectura, Reconocimiento, Sostenibilidad
- **Nivel de satisfacción**: 5 - Excelente
- **Seguimiento requerido**: Sí
- **Relación con OKRs**: Objetivos Q2 - Liderazgo arquitectural y mentoría

## Seguimiento de acciones previas
- Desarrollo de propuesta arquitectural para multitenancy (completado exitosamente)
- Liderazgo de Sprint y iniciativas arquitecturales (en progreso)
- Mentoría con Cristian (funcionando bien)

## Temas tratados
- Reconocimiento excepcional del crecimiento y liderazgo demostrado
- Sostenibilidad de la carga de liderazgo múltiple
- Balance entre tareas de código y acompañamiento soft
- Progreso en objetivos Q2 2025 y evaluación detallada
- Proyección de carrera y estado actual post-revisión de mitad de año
- Futuro de Synapse y posible rol en otros proyectos
- Coordinación con Santi en temas de frontend
- Iniciativas de nivelación para Junior/Semi Senior
- Gestión de promociones y evidencia técnica

## Logros desde la última reunión
- Reconocimiento transversal del equipo (Cris, Boni, Nico) por impacto generado
- Nuevos diagramas de arquitectura desarrollados y revisados
- Primera experiencia liderando Sprint exitosamente
- Ownership demostrado en reuniones y toma de decisiones
- Crecimiento en autonomía: "una vez orientado, toma el camino y sigue"
- Rol de referente técnico consolidado para múltiples equipos

## Logros no reportados
- Impacto transversal reconocido por equipos indirectos
- Consultas frecuentes de otros desarrolladores (Ivo, Tommy, Elián, Boni)
- Capacidad de liderazgo sin rehuir responsabilidades
- Madurez profesional demostrada en gestión de iniciativas

## Bloqueos reportados
- Diseño de arquitectura de integración con AI pendiente (10-15% progreso)
- Posible cliente PAMI que podría impactar carga de trabajo
- Balance entre tiempo de código y acompañamiento soft

## Desafíos actuales
- Sostenibilidad del liderazgo múltiple: "siento que lidero casi todas las iniciativas"
- Gestión de carga no lineal (lunes/martes/viernes intensos vs. miércoles/jueves relajados)
- Coordinación entre tareas técnicas y sociales con deadlines simultáneos
- Necesidad de coordinación con Santi para unificar criterios frontend

## Bienestar y equilibrio trabajo-vida
- Estado anímico positivo: "tirando para adelante como un burro con anteojeras"
- Enfoque determinado pero reconoce necesidad de balance
- Sin problemas de salud reportados
- Planificación de vacaciones para fin de año

## Feedback bidireccional
### Observaciones sobre Tobias
- **Crecimiento excepcional**: "Para mí has crecido junto con Mica, han sido los dos que han crecido más en el equipo"
- **Reconocimiento del equipo**: Impacto real demostrado y reconocido por colegas
- **Madurez de liderazgo**: "Noto una madurez alta, alta"
- **Capacidad arquitectural**: Diagramas y propuestas de alta calidad
- **Autonomía y ownership**: Toma iniciativas sin rehuir responsabilidades
- **Readiness para Senior**: Técnicamente cumple criterios para progresión

### Feedback para el líder
- **Apoyo valorado**: Reconocimiento del acompañamiento y "malla de seguridad"
- **Espacio para crecer**: Agradecimiento por ser "dejado a los leones" con respaldo
- **Visión de futuro**: Interés en capacitación transversal de product management

## Plan de Carrera (Observaciones / acciones)
- **Progresión a Senior**: Evaluación programada para final de Q2
- **Especialización en Arquitectura**: Track consolidado con Joel
- **Liderazgo sostenible**: Necesidad de estructurar para evitar sobrecarga
- **Capacitación product management**: Oportunidad identificada para Q3
- **Mentoría estructurada**: Formalización del acompañamiento a otros desarrolladores

## Métricas de crecimiento
| Habilidad/Área | Estado actual | Objetivo | Progreso |
|----------------|---------------|----------|----------|
| Liderazgo Arquitectural | Senior | Senior | Cumplido |
| Mentoría | Senior | Senior | Cumplido |
| Ownership | Senior | Senior | Cumplido |
| Integración IA | En progreso | Completar Q2 | 10-15% (bajo) |
| Capacitación System Design | En progreso | Completar Q2 | En track |

## Recursos de aprendizaje recomendados
- **Capacitación product management**: Programada para Q3 con nuevo recurso de Facu
- **Track de nivelación**: Desarrollo de sílabo para Junior/Semi Senior
- **Coordinación con Santi**: Sesiones de trabajo para unificar criterios frontend
- **Reunión con Farid**: Coordinación sobre integración IA

## Alineación con valores de la empresa
- **Liderazgo por ejemplo**: Genera impacto y reconocimiento genuino del equipo
- **Crecimiento continuo**: Evolución constante en capacidades técnicas y de liderazgo
- **Colaboración**: Apoyo activo a colegas y disposición para mentoría
- **Ownership**: Toma responsabilidad de iniciativas complejas sin rehuir

## Objetivos para la próxima reunión
- Revisar progreso en diseño de arquitectura de integración con IA
- Evaluar sostenibilidad de carga de liderazgo múltiple
- Seguimiento de coordinación con Santi
- Preparar evaluación formal para progresión a Senior
- Definir estructura a largo plazo para mentoría

## Acuerdos y acciones
| Acción | Responsable | Fecha límite | Prioridad |
|--------|-------------|--------------|-----------|
| Coordinar reunión con Farid sobre integración IA | Tobias | 05/06/2025 | Alta |
| Desarrollar sílabo de nivelación Junior/Semi Senior | Tobias/Mauricio | 30/06/2025 | Media |
| Reunión de trabajo con Santi para unificar criterios | Tobias | 05/06/2025 | Alta |
| Evaluación formal para progresión a Senior | Mauricio/Tobias | 30/06/2025 | Alta |
| Estructurar mentoría a largo plazo | Mauricio | 15/06/2025 | Media |
| Registrar horas BAU para tracking de carga | Tobias | Inmediato | Media |

## Notas adicionales
Esta reunión de 77 minutos fue excepcionalmente productiva y marcó un hito importante en el reconocimiento del crecimiento de Tobias. El Tech Lead fue explícito en destacar que Tobias, junto con Micaela, han sido "los dos que han crecido más en el equipo" y que su evolución ha sido "excepcional".

Un tema central fue la sostenibilidad del liderazgo múltiple. Tobias expresó honestamente que "siento que lidero casi todas las iniciativas", lo que generó una discusión importante sobre balance y distribución de responsabilidades. Se acordó mantener un máximo de 30-40% de tiempo en código, priorizando miércoles y jueves para tareas técnicas cuando la carga de reuniones es menor.

Se discutió extensamente el tema de promociones y evidencia técnica, donde Tobias mostró una perspectiva madura sobre los criterios de evaluación. Su comentario "si alguien hace todo lo que le das y completa todo, claramente está desaprovechado" demuestra una comprensión profunda de la progresión profesional.

La coordinación con Santi fue identificada como crucial, especialmente con su regreso parcial al proyecto. Tobias valoró esta incorporación dado que "no tengo mucha idea de cosas de frontend".

Se estableció un plan para desarrollar un track de nivelación para Junior/Semi Senior, aprovechando la experiencia de Tobias con el equipo y su capacidad de identificar gaps técnicos. Esta iniciativa se alinea con su objetivo Q2 de acompañamiento técnico.

La reunión concluyó con una clara proyección hacia la evaluación de final de Q2 para progresión a Senior, donde tanto el Tech Lead como Tobias expresaron confianza en el cumplimiento de criterios técnicos y de liderazgo.
