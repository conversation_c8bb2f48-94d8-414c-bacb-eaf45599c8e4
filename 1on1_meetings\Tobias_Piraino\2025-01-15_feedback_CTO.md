# Feedback CTO a Tobias - Enero 2025

## Información básica
- **Miembro del equipo**: <PERSON>
- **<PERSON><PERSON>**: 15/01/2025 (aproximada)
- **Ciclo de revisión**: Trimestral
- **<PERSON><PERSON> clave**: <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Técnico
- **Nivel de satisfacción**: 5 - Excelente
- **Proporcionado por**: CTO (líder directo en ese momento)

## Contexto
Este feedback fue proporcionado por el CTO (líder directo de Tobias en ese momento) en enero de 2025, aproximadamente 6 meses después de su incorporación a la empresa.

## Fortalezas y Logros

### Iniciativa y capacidad de aprendizaje
Expansión significativa de capacidades técnicas en solo 6 meses, especialmente en el dominio de cloud ops.

### Dominio técnico
Manejo excepcional de GCP, Apigee y Kubernetes. Capacidad para identificar y adoptar herramientas que permiten hacer más con menos (fundamental en contexto de mentalidad startup) y encarar arquitecturas desafiantes.

### Mentalidad de crecimiento
Disposición para dar y recibir feedback, base del crecimiento continuo y la mejora del desempeño.

### Pensamiento crítico
Destacó desde la primera semana por animarse a proponer mejoras tanto en iniciativas nuevas como en implementaciones existentes.

### Comunicación efectiva
Buen criterio para escalar dudas relevantes mientras resuelve detalles menores independientemente. Capacidad para hacer el trade-off entre qué comunicar y qué intentar resolver, importante para avanzar en el plan de carrera.

### Reconocimiento del equipo
Recibió reconocimiento en sus primeros meses, demostrando valoración por parte de líderes y equipo.

### Colaboración
Participación efectiva en múltiples proyectos (nom035, chaski, padrones, synapse) y apoyo en temas adicionales y bugs cuando tuvo oportunidad.

## Áreas de Oportunidad

### Producto sobre Tecnología
Si bien su expertise técnico es sólido, recordar que la tecnología es el medio, no el fin. Como señala Marty Cagan en "INSPIRED", las mejores soluciones técnicas son aquellas que resuelven problemas reales de los usuarios. Preguntarse siempre: "¿Cómo impacta esto en nuestros usuarios?". En Nom035 pudo poner esto a prueba en la práctica.

### Liderazgo Técnico
Potencial como líder natural. Su combinación de conocimiento y pasión puede ser inspiradora para otros. Recomendaciones:
- Compartir más activamente su conocimiento
- Mentorear a otros desarrolladores
- Liderar con el ejemplo en prácticas de desarrollo
- Lectura recomendada: "The Manager's Path"

### Innovación y Experimentación
"Si no estás avergonzado de la primera versión de tu producto, lo lanzaste demasiado tarde". En ÜMA, como en muchas startups exitosas, la innovación viene de atreverse a experimentar. El uso de IA y aproximaciones no tradicionales será crucial.

### Análisis vs. Acción
El "overthinking" es un desafío común entre desarrolladores talentosos. Como dice Eric Ries en "The Lean Startup", la única forma de aprender realmente es poniendo las cosas en manos de los usuarios. Adoptar más el principio de "Construir->Medir->Aprender->Iterar":
- Construir MVPs más pequeños
- Establecer métricas claras
- Iterar basándose en feedback real

## Relación con la reunión del 11/03/2025
Este feedback proporciona contexto importante para la reunión extraordinaria del 11/03/2025 con el CTO, donde se discutió el riesgo de renuncia de Tobias. Varios elementos mencionados en este feedback (potencial de liderazgo, enfoque en producto sobre tecnología, capacidad de comunicación) se alinean con las preferencias expresadas por Tobias en esa reunión (planificación sobre ejecución, bajada de productos a tecnología, liderar personas).

Referencia:
https://docs.google.com/document/d/1lWVjF-mE8uH638jRFlYXt5_IYgcVo5hyV7HINH0Nf_I/edit?tab=t.0
