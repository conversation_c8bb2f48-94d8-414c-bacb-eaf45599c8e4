# 1:1 con Javier - 01/07/2025

## Información básica
- **Miembro del equipo**: <PERSON>
- **Fecha**: 01/07/2025
- **Duración**: 51 minutos (10:01 - 10:52)
- **Ciclo de revisión**: Regular + Evaluación Q2
- **Temas clave**: Autonomía, Exigencias, Monitoreo, Capacitación, Procesos
- **Nivel de satisfacción**: 3 - Regular (frustración por tareas Junior)
- **Seguimiento requerido**: Sí
- **Relación con OKRs**: Consolidación Q2 + Planificación Q3/Q4

## Seguimiento de acciones previas
- Separación de repos API y código (megalito): Completado
- Pipeline Trunk Based Development con Cloud Deploy: Implementado
- Mejoras de seguridad (OWASP Top 10, ingress internal): Completadas
- Coordinación con Aeros: Documento propositivo entregado

## Temas tratados
Prioridad: Alta
- **Consolidación Q3/Q4**: Transición de apoyo a exigencia en autonomía de equipos
- **Monitoreo centralizado**: Necesidad urgente de logging y alertas bien definidas
- **Capacitación CKA**: Planificación de 9 horas semanales para certificación
- **Autonomía de equipos**: Exigir independencia en tareas básicas de DevOps
- **Procesos de preventa**: Inclusión temprana de Cloud/Security/Data en proyectos
- **Evaluación Bisneo**: Problemas con criterios de evaluación inadecuados
- **Análisis de gastos**: Colaboración con finanzas para análisis completo

## Logros desde la última reunión
- **Separación de repos**: Megalito dividido exitosamente en API y código
- **Pipeline CI/CD**: Trunk Based Development con Cloud Deploy implementado
- **Mejoras de seguridad**: OWASP Top 10 e ingress internal configurados
- **Documento Aeros**: Propuesta técnica bien estructurada y propositiva
- **Sonarqube**: Configuración en megalito lista para Quality Gates

## Logros no reportados
- **Gestión de crisis**: Resolución efectiva de múltiples incidentes técnicos
- **Mentoría técnica**: Apoyo continuo a equipos en temas de infraestructura
- **Optimización de costos**: Identificación de gastos hormiga por $2000/mes
- **Coordinación técnica**: Facilitación de comunicación entre equipos

## Bloqueos reportados
- **Falta de monitoreo**: Sin logging centralizado ni alertas definidas en servicios críticos
- **Recursos limitados**: Único DevOps para múltiples crisis simultáneas
- **Equipos no capacitados**: Parte del equipo no resuelve problemas básicos propios
- **Procesos de preventa**: Cloud/Security/Data no incluidos en early stages

## Desafíos actuales
- **Implementar exigencias Q3**: Transición de apoyo a autonomía obligatoria
- **Definir Quality Gates**: Configuración de umbrales para Sonarqube
- **Monitoreo centralizado**: Estrategia integral con participación de todos los equipos
- **Capacitación MLOps**: Definir track específico para equipo de IA
- **Estandarización Docker**: Misma imagen para 3 entornos en proyectos NextJS

## Bienestar y equilibrio trabajo-vida
- **Carga de trabajo**: Frustración por resolver mayormente tareas Junior
- **Motivación**: Afectada por ser único DevOps durante 5 años
- **Trabajo interesante**: Falta de proyectos desafiantes de arquitectura cloud
- **Gastos personales**: Inversión significativa en puerta de seguridad afecta finanzas
- **Vacaciones Australia**: Canceladas por costos ($2200 USD solo pasaje)

## Feedback bidireccional
### Observaciones sobre Javier
- **Documento Aeros**: "Me gusta, está muy propositivo, excelente"
- **Liderazgo técnico**: Conducción efectiva de reuniones técnicas
- **Visión estratégica**: Comprensión clara de necesidades de consolidación
- **Experiencia valiosa**: "Eres un referente en estas cosas"
- **Propuestas constructivas**: Ideas claras para mejoras de proceso

### Feedback para el líder
- **Apoyo en prioridades**: Agradecimiento por filtrar solicitudes y ordenar tareas
- **Comprensión**: Valoración de entendimiento de desafíos técnicos
- **Coordinación**: Reconocimiento de esfuerzos por mejorar procesos

## Plan de Carrera (Observaciones / acciones)
- **Certificación CKA**: 9 horas semanales bloqueadas para estudio
- **Especialización Kubernetes**: Enfoque en tecnologías cloud nativas
- **Liderazgo técnico**: Consolidación como referente en DevOps/Cloud
- **Proyectos arquitectura**: Objetivo de encarar proyectos más desafiantes
- **Contratación equipo**: Necesidad de ampliar equipo DevOps

## Métricas de crecimiento
| Habilidad/Área | Estado actual | Objetivo | Progreso |
|----------------|---------------|----------|----------|
| Kubernetes/CKA | Avanzado | Certificado | 70% |
| Liderazgo técnico | Senior | Principal | 85% |
| Arquitectura Cloud | Senior | Especialista | 80% |
| Gestión de equipos | Individual | Líder equipo | 60% |
| Procesos DevOps | Experto | Evangelista | 90% |

## Recursos de aprendizaje recomendados
- **Certificación CKA**: 9 horas semanales de estudio estructurado
- **MLOps track**: Investigación de certificaciones específicas para equipo IA
- **Procesos de preventa**: Participación en definición de nuevos workflows
- **Análisis financiero**: Colaboración con finanzas para optimización de costos

## Alineación con valores de la empresa
- **Excelencia técnica**: Implementación de mejores prácticas en todos los proyectos
- **Colaboración**: Facilitación de comunicación entre equipos técnicos
- **Eficiencia**: Optimización continua de procesos y costos
- **Innovación**: Propuestas proactivas para mejoras técnicas

## Objetivos para la próxima reunión
- Definir Quality Gates con Farid para Sonarqube
- Avanzar en planificación de monitoreo centralizado
- Establecer porcentajes de tiempo para estudio CKA en Q3
- Revisar progreso en exigencias de autonomía a equipos
- Evaluar necesidades de contratación para equipo DevOps

## Acuerdos y acciones
| Acción | Responsable | Fecha límite | Prioridad |
|--------|-------------|--------------|-----------|
| Definir Quality Gates Sonarqube | Mauricio/Farid | 08/07/2025 | Alta |
| Planificar monitoreo centralizado | Mauricio | 15/07/2025 | Alta |
| Mapeo tiempo estudio CKA Q3 | Mauricio/Javier | 10/07/2025 | Media |
| Investigar MLOps certifications | Mauricio | 12/07/2025 | Media |
| Reunión consultiva con Nahuel (finanzas) | Mauricio | 08/07/2025 | Media |

## Notas adicionales
Esta reunión de 51 minutos se centró en la transición estratégica de Q2 a Q3/Q4, marcando un cambio fundamental de apoyo a exigencia en la autonomía de los equipos de desarrollo.

**Contexto Personal - Gastos e Inversiones**: Javier realizó una inversión significativa en una puerta de seguridad que excedió sus expectativas de costo en 40%, afectando sus planes de vacaciones a Australia para visitar familiares. Esta situación financiera temporal no afecta su rendimiento laboral pero explica cierta tensión personal.

**Consolidación Q3/Q4 - Cambio de Paradigma**: El Tech Lead estableció claramente que Q2 fue de apoyo y capacitación, pero Q3/Q4 será de exigencia y autonomía. "Ahora hay que entrar a exigir y presionar que funcione". Los equipos deben operar independientemente las capacitaciones recibidas.

**Monitoreo Centralizado - Prioridad Crítica**: Javier expresó frustración por la falta de monitoreo y alertas: "Es molesto no tener tiempo ni recurso humano para armar esto y enterarme que las cosas se rompieron fuera de horario laboral". Necesidad de involucrar a todos los equipos en la definición de métricas.

**Único DevOps - Problema Estructural**: "5 años, sigo siendo el único DevOps" - Javier mantiene esta responsabilidad crítica sin respaldo, limitando su capacidad de crecimiento y generando riesgo operacional. El Tech Lead reconoce y escala este problema constantemente.

**Tareas Junior vs Trabajo Interesante**: Frustración expresada por resolver "cosas de Junior" sin acceso a "proyectos interesantes de arquitectura cloud". Necesidad de redistribuir responsabilidades básicas a equipos capacitados.

**Procesos de Preventa - Mejora Crítica**: Identificación de problema sistémico donde Cloud/Security/Data se enteran de requerimientos cuando el proyecto ya está vendido y en ejecución. "No puede ser que el proyecto termine cuando ya está vendido y estamos empezando a trabajar".

**Evaluación Bisneo - Problemas de Criterio**: Javier criticó duramente las evaluaciones: "La mitad de las preguntas carecen de sentido. ¿Cuál cliente?" Reconocimiento del Tech Lead sobre necesidad de cambiar criterios de evaluación para área técnica.

**Certificación CKA - Desarrollo Profesional**: Planificación de 9 horas semanales (3 días x 3 horas) para preparación de certificación Kubernetes. Reconocimiento como necesidad in-house para la empresa.

**MLOps Certification - Consulta Técnica**: Javier asesoró sobre certificación GCP Professional DevOps para equipo de IA, recomendando que no es adecuada por enfoque en Kubernetes vs herramientas MLOps específicas. Tarea pendiente de investigar alternativas.

**Análisis de Gastos - Colaboración Finanzas**: Propuesta de Javier para incluir a finanzas en análisis de costos cloud, identificando limitaciones del análisis actual por falta de visión integral. Reunión consultiva programada con Nahuel.

**Quality Gates Sonarqube**: Configuración técnica lista pero pendiente de definición de umbrales con Farid. Ejemplo de coordinación necesaria entre liderazgo técnico y dirección.

**Autonomía de Equipos - Exigencia Inmediata**: Identificación clara de equipos que no resuelven problemas básicos propios. Estrategia de Q3: "Ya se les explicó, es parte de su responsabilidad, no pueden estar levantando la mano constantemente".

**Gestión de Crisis - Limitación Operacional**: "Si hay 2 eventos/crisis que necesitan urgente alguien de cloud, no se pueden resolver ambos" - Problema estructural que requiere solución organizacional.

**Estandarización Docker**: Necesidad de que equipos de desarrollo modifiquen proyectos NextJS para usar misma imagen en 3 entornos, facilitando implementación de pipelines Cloud Deploy.

**Cultura de Gestión de Incidentes**: El Tech Lead identificó que los problemas reportados son "sintomatología de un proceso más grande enclaustrado en la cultura de UMA" relacionado con gestión de incidentes y ownership.

La reunión concluyó con un plan claro de transición hacia mayor exigencia de autonomía, reconocimiento de limitaciones estructurales, y compromiso de escalamiento de problemas sistémicos a nivel directivo.
