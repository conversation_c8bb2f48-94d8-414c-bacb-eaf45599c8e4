Puesto: Staff Engineer (IC Track) - Con Especializaciones

Seniority: Staff Engineer

Level: 5

CERTIFICACIÓN BASE OBLIGATORIA:
• Google Cloud Associate Cloud Engineer (prerequisito para todas las especializaciones)

ESPECIALIZACIONES DISPONIBLES:
Al llegar a Staff Engineer, se debe elegir UNA especialización y obtener la certificación Professional correspondiente:

1. STAFF ENGINEER - SECURITY SPECIALIST
   Certificación: Google Cloud Professional Cloud Security Engineer
   
2. STAFF ENGINEER - ARCHITECTURE SPECIALIST  
   Certificación: Google Cloud Professional Cloud Architect
   
3. STAFF ENGINEER - DEVSECOPS SPECIALIST
   Certificación: Google Cloud Professional Cloud DevOps Engineer
   
4. STAFF ENGINEER - MLOPS SPECIALIST
   Certificación: Google Cloud Professional Machine Learning Engineer
   
5. STAFF ENGINEER - QUALITY SPECIALIST
   Certificación: Google Cloud Professional Cloud Developer + Certificaciones QA externas

=== RESPONSABILIDADES COMUNES A TODAS LAS ESPECIALIZACIONES ===

Responsabilidades Generales:
- Diseñar arquitectura de sistemas completos
- Definir estándares técnicos para toda la organización
- Liderar múltiples proyectos o iniciativas técnicas
- Evaluar y recomendar nuevas tecnologías
- Optimizar procesos de desarrollo a nivel organizacional
- Guiar decisiones tecnológicas críticas y estratégicas
- Colaborar con liderazgo ejecutivo en planificación técnica
- Resolver problemas técnicos de alta complejidad
- Actuar como referente técnico dentro y fuera de la organización
- Anticipar necesidades técnicas futuras
- Mentorizar a Senior Engineers
- Diseña sistemas con métricas de éxito claras. Implementa herramientas y flujos de observabilidad
- Promover un enfoque flexible en la toma de decisiones técnicas
- Asegurar que las soluciones arquitectónicas están alineadas con la experiencia del usuario final
- LIDERAR EN SU ÁREA DE ESPECIALIZACIÓN a nivel organizacional

Funciones Principales:
Innovación
• Define estrategias tecnológicas que posicionan a la empresa en la vanguardia del sector
• Lidera iniciativas de innovación en arquitectura, rendimiento y procesos de desarrollo
• Identifica tendencias de la industria y traduce esas oportunidades en mejoras organizacionales

Alineación con los OKRs de la empresa
• Define estrategias arquitectónicas que permiten cumplir los objetivos de escalabilidad y rendimiento
• Asegura que las decisiones técnicas estén alineadas con la visión del negocio a largo plazo
• Lidera iniciativas para optimizar costos de infraestructura sin afectar la calidad del servicio

=== ESPECIALIZACIONES DETALLADAS ===

1. STAFF ENGINEER - SECURITY SPECIALIST
Certificación Requerida: Google Cloud Professional Cloud Security Engineer

Responsabilidades Específicas:
- Diseñar e implementar arquitecturas de seguridad cloud-native
- Liderar iniciativas de Zero Trust y compliance organizacional
- Definir políticas de seguridad para toda la organización
- Evaluar y mitigar riesgos de seguridad a nivel de sistemas
- Implementar estrategias de DevSecOps y security by design
- Liderar respuesta a incidentes de seguridad críticos
- Establecer programas de security awareness y training

Skills Técnicas Adicionales:
- Expertise en IAM, VPC Security, Cloud KMS, Security Command Center
- Conocimiento profundo de compliance (SOC2, ISO27001, GDPR)
- Experiencia en penetration testing y vulnerability assessment
- Dominio de herramientas de security scanning y monitoring

2. STAFF ENGINEER - ARCHITECTURE SPECIALIST
Certificación Requerida: Google Cloud Professional Cloud Architect

Responsabilidades Específicas:
- Diseñar arquitecturas de referencia para la organización
- Liderar decisiones de arquitectura multi-cloud y híbrida
- Definir patrones arquitectónicos y estándares de diseño
- Evaluar y seleccionar tecnologías para adopción organizacional
- Liderar iniciativas de modernización de arquitectura legacy
- Establecer governance de arquitectura y ADRs (Architecture Decision Records)
- Mentorizar en diseño de sistemas distribuidos y escalables

Skills Técnicas Adicionales:
- Expertise en microservicios, event-driven architecture, CQRS
- Dominio de patrones cloud-native y serverless
- Conocimiento profundo de networking, load balancing, CDN
- Experiencia en disaster recovery y business continuity

3. STAFF ENGINEER - DEVSECOPS SPECIALIST
Certificación Requerida: Google Cloud Professional Cloud DevOps Engineer

Responsabilidades Específicas:
- Diseñar e implementar pipelines CI/CD organizacionales
- Liderar iniciativas de Infrastructure as Code y GitOps
- Establecer estrategias de monitoring, logging y observabilidad
- Implementar prácticas de Site Reliability Engineering (SRE)
- Definir SLIs, SLOs y error budgets organizacionales
- Liderar automatización de procesos de deployment y rollback
- Establecer cultura DevOps y mejores prácticas

Skills Técnicas Adicionales:
- Expertise en Kubernetes, Terraform, Ansible
- Dominio de herramientas de monitoring (Prometheus, Grafana, etc.)
- Conocimiento profundo de CI/CD tools y estrategias
- Experiencia en chaos engineering y reliability testing

4. STAFF ENGINEER - MLOPS SPECIALIST
Certificación Requerida: Google Cloud Professional Machine Learning Engineer

Responsabilidades Específicas:
- Diseñar e implementar pipelines de ML en producción
- Liderar iniciativas de MLOps y ML governance
- Establecer estrategias de model versioning y deployment
- Implementar monitoring de model drift y performance
- Definir arquitecturas para data lakes y feature stores
- Liderar iniciativas de responsible AI y ethics
- Establecer prácticas de experimentation y A/B testing para ML

Skills Técnicas Adicionales:
- Expertise en Vertex AI, BigQuery ML, TensorFlow
- Dominio de data engineering y pipeline orchestration
- Conocimiento profundo de ML lifecycle management
- Experiencia en model serving y real-time inference

5. STAFF ENGINEER - QUALITY SPECIALIST
Certificación Requerida: Google Cloud Professional Cloud Developer + Certificaciones QA

Responsabilidades Específicas:
- Diseñar e implementar estrategias de testing organizacionales
- Liderar iniciativas de quality assurance y automation
- Establecer frameworks de testing (unit, integration, e2e)
- Implementar estrategias de performance y load testing
- Definir métricas de calidad y coverage standards
- Liderar iniciativas de shift-left testing
- Establecer cultura de quality engineering

Skills Técnicas Adicionales:
- Expertise en testing frameworks y automation tools
- Dominio de performance testing y chaos engineering
- Conocimiento profundo de quality metrics y reporting
- Experiencia en test data management y environment provisioning

Certificaciones QA Recomendadas:
- ISTQB Advanced Level Test Analyst
- Certified Agile Testing (CAT)
- Selenium WebDriver Certification

=== SKILLS COMUNES A TODAS LAS ESPECIALIZACIONES ===

Formación académica adicional:
- Título universitario completo preferido
- Formación continua: cursos avanzados, certificaciones Cloud
- Alto compromiso con aprendizaje continuo y liderazgo técnico

Requisitos de experiencia: +5 años

Soft Skills requeridas:
- Comunicación y Colaboración
- Comprensión de las necesidades del cliente
- Adaptabilidad y Agilidad
- Capacidad Analítica - Data-Centric Thinking
- Empatía
- Innovación
- Pensamiento Crítico
- Autonomía e Impacto
- Liderazgo y Mentoring
- Resolución de Conflictos

Próximo Nivel: Principal Engineer (futuro) / Tech Lead (cambio de track)

=== CRITERIOS DE PROGRESIÓN ===

Para ser promovido a Staff Engineer (cualquier especialización):
1. Completar certificación GCP Associate Cloud Engineer
2. Demostrar expertise en el área de especialización elegida
3. Liderar exitosamente iniciativas técnicas de impacto organizacional
4. Obtener la certificación Professional correspondiente a la especialización
5. Demostrar capacidad de mentoría y liderazgo técnico
6. Mostrar impacto medible en la calidad, seguridad, o eficiencia organizacional
