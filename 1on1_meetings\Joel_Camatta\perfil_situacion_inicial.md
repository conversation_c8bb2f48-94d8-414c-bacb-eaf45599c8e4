# Perfil y Situación Inicial - <PERSON>

## Información básica
- **Nombre**: <PERSON>
- **Rol actual**: Backend Developer (catalogado como Junior)
- **Experiencia previa**: Cloud Engineer con experiencia en Python
- **Tiempo en la empresa**: Aproximadamente 1 año (desde junio 2024)
- **Formación académica**: Estudios incompletos de Física y Matemáticas universitarias
- **Estilo de aprendizaje**: Autodidacta en desarrollo de software
- **Fecha de análisis inicial**: 19/03/2025
- **Fecha de primera reunión**: 20/03/2025
- **Líderes asignados**:
  - <PERSON> (Tech Lead directo - aspectos técnicos)
  - <PERSON><PERSON><PERSON> (Tech Lead - crecimiento profesional, personal y plan de carrera)

## Contexto de la situación

Joel ingresó a la empresa como parte del área de MLOps, aceptando ser recatalogado como Junior a pesar de su experiencia previa como Cloud Engineer. Esta decisión fue motivada por su interés en el campo de la Inteligencia Artificial. Sin embargo, actualmente está trabajando 100% como Backend Developer en el equipo liderado por Santiago, ya no forma parte del equipo de IA/MLOps, mientras mantiene una compensación de nivel Junior.

Al inicio del proyecto actual, Joel asumió responsabilidades de interacción con clientes (conversaciones, acuerdos), pero fue liberado de estas tareas cuando Santiago se incorporó, lo que le ha permitido sentirse más tranquilo.

## Áreas de fortaleza
- Experiencia previa como Cloud Engineer
- Interés y conocimientos en Inteligencia Artificial
- Aceptación de desafíos y disposición para aprender nuevas áreas
- Toma ownership de sus tareas
- Buenas habilidades sociales
- Capacidad para capacitar a otros y diseñar servicios
- Desarrollo sin documentación técnica completa
- Evaluado como nivel Semi Senior en términos de capacidades

## Áreas de mejora identificadas
- **Tendencia a la sobreingeniería**: Identificado como un punto principal a mejorar
- **Agilidad**: Necesidad de mejorar velocidad y eficiencia
- **Habilidades de comunicación**: Considera que tiene buen nivel pero reconoce que siempre hay espacio para mejorar
- **Integración de IA en su trabajo**: Importante incorporar más este aspecto, alineado con su interés inicial
- **Seguridad técnica**: Necesita "pulir" temas técnicos y desarrollar mayor seguridad al definir aspectos técnicos

## Estructura de liderazgo compartido

Se ha establecido un modelo de liderazgo compartido para el desarrollo de Joel:

1. **Santiago (Tech Lead directo)**:
   - Responsable de la supervisión técnica directa
   - Guía en aspectos técnicos del día a día
   - Feedback sobre calidad de código y prácticas de desarrollo

2. **Mauricio (Tech Lead - desarrollo)**:
   - Enfoque en crecimiento profesional y personal
   - Desarrollo del plan de carrera
   - Alineación de intereses personales (IA) con necesidades de la empresa

## Consideraciones especiales

- **Discrepancia entre nivel y experiencia**: Joel tiene capacidades de nivel Semi Senior pero está catalogado y compensado como Junior
- **Interés no alineado con trabajo actual**: Ingresó por interés en IA pero trabaja 100% en backend
- **Potencial subaprovechado**: Posible oportunidad de aprovechar mejor sus conocimientos previos en Cloud
- **Aspiraciones a largo plazo**: Interés en tener su propia empresa
- **Intereses de desarrollo**: Enfoque en el track de Individual Contributor, con interés en diseño y arquitectura de sistemas
- **Plan de progresión**: Evaluado con potencial para crecer a Senior en Q2 2025

## Próximos pasos recomendados

1. **Implementación de capacitación**: Incorporar a Joel en la capacitación interna de System Design & Arquitectura de Sistemas junto con Tobias durante Q2 2025
2. **Evaluación detallada de habilidades**: Determinar el nivel real de Joel en backend y sus conocimientos en IA
3. **Plan para abordar áreas de mejora**: Especialmente sobreingeniería, seguridad técnica y agilidad
4. **Revisión de nivel y compensación**: Preparar plan para progresión a Senior en Q2 2025
5. **Establecimiento de expectativas claras**: Definir qué se espera de Joel y qué puede esperar él en términos de desarrollo profesional
6. **Fomento de colaboración con Tobias**: Aprovechar intereses similares en diseño y arquitectura para aprendizaje mutuo

## Notas adicionales

Esta situación presenta tanto desafíos como oportunidades. Por un lado, existe el riesgo de desmotivación si Joel siente que sus habilidades están siendo subvaloradas o si no puede trabajar en áreas de su interés. Por otro lado, hay una oportunidad para desarrollar un plan de carrera que integre su experiencia en Cloud, su interés en IA y las necesidades actuales de desarrollo backend de la empresa.
