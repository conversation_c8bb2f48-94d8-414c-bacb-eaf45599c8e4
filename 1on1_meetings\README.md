# Reuniones 1:1 - UMA HEALTH

Este directorio contiene los registros locales de las reuniones 1:1 con los miembros del equipo. Cada reunión se guarda como un archivo Markdown dentro de la carpeta correspondiente al miembro del equipo.

## Estructura de carpetas

```
1on1_meetings/
├── <PERSON>/    # DevOps
│   └── YYYY-MM-DD_1on1.md
├── Tobias_<PERSON>raino/      # Backend Developer / Semi Senior
│   └── YYYY-MM-DD_1on1.md
├── <PERSON>/        # Staff Developer / Especialista
│   └── YYYY-MM-DD_1on1.md
├── <PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>/   # Backend Developer / Semi Senior
│   └── YYYY-MM-DD_1on1.md
├── <PERSON><PERSON><PERSON>_<PERSON>ru<PERSON>/      # Backend Developer / Junior
│   └── YYYY-MM-DD_1on1.md
└── Joel_Camatta/        # Backend Developer / Junior (Liderazgo compartido)
    ├── README.md                    # Explicación del modelo de liderazgo compartido
    ├── perfil_situacion_inicial.md  # Contexto y situación inicial
    ├── plan_desarrollo_carrera.md   # Plan de desarrollo profesional
    └── YYYY-MM-DD_1on1.md           # Reuniones 1:1 enfocadas en desarrollo profesional
```

## Formato de archivos

Cada archivo sigue la plantilla mejorada para reuniones 1:1, que incluye:

- Información básica (miembro, fecha, duración, etc.)
- Seguimiento de acciones previas
- Temas tratados
- Logros y bloqueos
- Feedback bidireccional
- Plan de carrera
- Objetivos y acciones

## Flujo de trabajo

1. **Antes de la reunión**: Crear un nuevo archivo usando la plantilla en `templates/1on1_meeting_template.md`
2. **Durante la reunión**: Tomar notas en el archivo
3. **Después de la reunión**:
   - Guardar el archivo en la carpeta correspondiente con el formato `YYYY-MM-DD_1on1.md`
   - Compartir con Augment para enviar a Notion

## Sincronización con Notion

Estos archivos locales sirven como respaldo y referencia rápida. La información también se sincroniza con la base de datos de Notion "Reuniones 1:1" para facilitar la búsqueda, filtrado y análisis de tendencias.

## Casos especiales

### Joel Camatta - Modelo de liderazgo compartido

Joel Camatta se encuentra bajo un modelo de liderazgo compartido:

- **Santiago (Tech Lead directo)**: Responsable de aspectos técnicos, supervisión diaria y feedback técnico
- **Mauricio (Tech Lead)**: Responsable de crecimiento profesional, personal y plan de carrera

Las reuniones 1:1 documentadas en esta estructura corresponden únicamente a las sostenidas con Mauricio, enfocadas en desarrollo profesional. Para más detalles sobre este modelo, consultar el archivo `Joel_Camatta/README.md`.
