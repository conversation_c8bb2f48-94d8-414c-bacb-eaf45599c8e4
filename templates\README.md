# Instrucciones para el uso de la plantilla de reuniones 1:1

## Flujo de trabajo

1. **Preparación para la reunión**:
   - Copia el contenido del archivo `1on1_meeting_template.md`
   - Completa la plantilla con la información relevante para la reunión
   - Elimina las secciones que no sean aplicables o déjalas en blanco

2. **Durante la reunión**:
   - Utiliza la plantilla como guía para la conversación
   - Toma notas directamente en la plantilla
   - Asegúrate de capturar acciones claras con responsables y fechas

3. **Después de la reunión**:
   - Revisa y finaliza las notas
   - Comparte la plantilla completada con Augment para enviarla a Notion
   - Augment creará una nueva entrada en la base de datos de Notion con toda la información estructurada

## Miembros del equipo

- **<PERSON>** (Javi) - DevOps
- **<PERSON>** (<PERSON><PERSON> o <PERSON>) - Backend Developer / Semi Senior
- **<PERSON><PERSON><PERSON>** (<PERSON>) - Staff Developer / Especialista
- **<PERSON><PERSON><PERSON>** (Mica) - Backend Developer / Semi Senior
- **<PERSON><PERSON><PERSON>** (Cris) - Backend Developer / Junior

## Propiedades de la base de datos

- **Temas clave**: Categorías principales discutidas (Técnico, Carrera, Proyecto, Personal, Feedback, Bienestar, Objetivos)
- **Nivel de satisfacción**: Evaluación de la calidad de la reunión (1-5)
- **Relación con OKRs**: Cómo se conectan los temas con los objetivos del equipo/empresa
- **Seguimiento requerido**: Si se necesita seguimiento especial (Sí/No)
- **Estado**: Estado de la reunión (Pendiente, Completada, Reprogramada, Cancelada)
- **Ciclo de revisión**: Contexto de la reunión (Regular, Trimestral, Semestral, Anual, Onboarding, Especial)

## Consejos para reuniones 1:1 efectivas

- Mantén un calendario regular de reuniones
- Permite que el miembro del equipo establezca parte de la agenda
- Equilibra la discusión de problemas actuales y desarrollo a largo plazo
- Documenta durante la reunión para no perder detalles importantes
- Haz seguimiento riguroso de las acciones acordadas
- Adapta el formato según las necesidades específicas de cada miembro del equipo
