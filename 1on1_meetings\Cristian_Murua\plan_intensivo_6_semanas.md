# Plan Intensivo de 6 Semanas - Tobias & Cristian

## Resumen del Plan
Este documento detalla el plan intensivo de 6 semanas formalizado por Tobias para apoyar a Cristian en su desarrollo técnico, con enfoque en fundamentos, NestJS, arquitectura de microservicios, patrones de diseño e integración de IA.

## Estructura Semanal

### 🔹 Semana 1: Fundamentos generales y entorno

**Objetivo**: Comprender la base informática y el entorno de desarrollo.

**Temas**:
- Qué es una API, protocolo HTTP/HTTPS, TCP/IP, puertos, headers, status codes
- REST vs RPC vs GraphQL (introductorio)
- IA: Usar IA para aclarar conceptos, no para reemplazar el aprendizaje. Mostrar cómo hacer prompts efectivos

**Accionable**:
- CRUD REST 100% tradicional con referencias a documentación para respaldar decisiones
- Investigar cómo maneja la conexión TCP NestJS

**Documentación**:
- Todos los PROTOCOLOS DE RED explicados fácil en 5 minutos 2022 (Parte 1) | IP, IPsec, ICMP, L2TP...
- Todos los PROTOCOLOS DE RED explicados fácil en 5 minutos (Parte 2) | TCP, SSH, HTTP, DNS, RIP
- gRPC vs REST - KEY differences and performance TEST
- gRPC vs REST vs GraphQL: Comparison & Performance
- https://www.geeksforgeeks.org/tcp-ip-model/

### 🔹 Semana 2: Fundamentos de NestJS

**Objetivo**: Comprender el framework principal del proyecto.

**Temas**:
- Módulos, controladores, servicios
- Pipes, Guards, DTOs
- Inyección de dependencias
- Estructura básica de un proyecto Nest
- IA: Aprender a generar scaffolds Nest con ayuda de IA y luego refactorizar a mano

**Accionable**:
- Utilizar IA para crear una funcionalidad, definir primero los DTO de req y res y cómo va a tener que ser el flujo, y luego indicarle cómo hacerlo entendiendo el flujo completo

**Documentación**:
- Playlist muy buena sobre Nest
- Medium
- NestJs (documentación oficial completa)

### 🔹 Semana 3: Arquitectura y Microservicios

**Objetivo**: Comprender cómo se estructura y comunica un sistema distribuido.

**Temas**:
- Qué es un microservicio y por qué se usa
- Comunicación entre microservicios: HTTP, queues, eventos
- NestJS + RabbitMQ o Kafka o TCP (introductorio)
- Principio de responsabilidad única (SRP) y separación de capas
- IA: Usar IA para visualizar flujos entre servicios y proponer esquemas alternativos

**Accionable**:
- Simular 2 microservicios que se comuniquen entre sí por HTTP o mensajes (Nest)

**Documentación**:
- Monolithic vs Microservice Architecture: Which To Use and When?
- Learn NestJS Microservices in 20 Minutes
- RabbitMQ + NestJS Microservices Crash Course | A Simple Order Processing Project
- Kafka in NestJS Microservices 🚀 | Real-Time Messaging with Apache Kafka
- Un medium básico recomendado

### 🔹 Semana 4: Patrones de diseño aplicados

**Objetivo**: Entender y aplicar patrones reales en el código.

**Temas**:
- Strategy, Factory, Observer, Singleton, Adapter
- Inversión de dependencias + principio SOLID
- IA: Analizar código de patrones y refactorizar uno con ayuda de la IA

**Accionable**:
- Aplicar un patrón para mejorar alguna feature (ya sea en megalito o synapse)

**Documentación**:
- Playlist de Patterns
- 20 System Design Concepts Explained in 10 Minutes
- Medium
- Refactor Guru
- Libro de patrones básico (disponible bajo petición)

### 🔹 Semana 5: Integraciones e IA aplicada

**Objetivo**: Aprender a integrar IA sin delegar todo.

**Temas**:
- Cursor básico
- IA como asistente de validación, resúmenes, análisis
- Prompt Engineering y revisión del output
- Reglas para IDEs

**Accionable**:
- Integrar un endpoint en Nest que use IA (por ejemplo, para resumir historia clínica simulada)
- Generar una documentación para Synapse donde se defina a Cursor el contexto general del proyecto

### 🔹 Semana 6: Consolidación y mini proyecto

**Objetivo**: Consolidar todos los conocimientos y trabajar como si estuviera en producción.

**Temas**:
- Documentación de endpoints
- Buenas prácticas: logs, errores, seguridad básica

**Accionable**:
- Armar un mini proyecto que conecte microservicios Nest, use al menos 1 patrón
- Uno de los microservicios debe encargarse de interactuar con una IA
- Presentarlo y explicarlo en una reunión (simulación de revisión de sprint)

## Seguimiento y Evaluación

### Puntos de Control
- Reunión semanal de revisión de progreso
- Evaluación de accionables completados
- Feedback específico sobre implementaciones
- Ajustes al plan según necesidades identificadas

### Métricas de Éxito
- Completitud de accionables semanales
- Comprensión demostrada de conceptos clave
- Calidad de implementaciones
- Capacidad para explicar decisiones técnicas
- Progreso en autonomía técnica

## Conexión con Objetivos Q2 y Plan de Carrera

Este plan intensivo se alinea directamente con:

1. **Objetivo personal de Cristian**: "Fortalecer capacidades técnicas participando en al menos 1 sesión de pairing y/o code review semanal guiada por un miembro más senior"

2. **Objetivo transversal de Tobias**: "Acompañamiento técnico a miembros del equipo (Mentorizar técnicamente a 1 miembro del equipo con menor experiencia con seguimiento semanal o quincenal)"

3. **Interés de Cristian en IA**: El plan incorpora elementos de IA aplicada, alineándose con el interés expresado por Cristian en la reunión del 07/05/2025

4. **Desarrollo de habilidades Junior a Semi-Senior**: El plan aborda sistemáticamente las habilidades técnicas necesarias para la progresión en el plan de carrera
