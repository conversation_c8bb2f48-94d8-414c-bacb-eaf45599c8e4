# 1:1 con <PERSON> - 17/03/2025

## Información básica
- **Miembro del equipo**: <PERSON>
- **<PERSON>cha**: 17/03/2025
- **Duración**: 45 minutos (16:00 - 16:45)
- **Ciclo de revisión**: Regular
- **<PERSON><PERSON> clave**: <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Infraestructura, Carrera
- **Nivel de satisfacción**: 4 - <PERSON><PERSON> buena
- **Seguimiento re<PERSON>**: Sí
- **Relación con OKRs**: Mejora de procesos DevOps y cultura técnica

## Seguimiento de acciones previas
*No se mencionan acciones específicas de reuniones anteriores*

## Temas tratados
- Preocupaciones sobre la cultura técnica de la empresa
- Responsabilidades entre equipos de desarrollo y DevOps
- APIs de la empresa y su exposición
- Comparativa entre Cloud Run y Apigee
- Estrategias de autenticación externa
- Cultura de MVP y riesgos operacionales
- Oportunidades de crecimiento personal/profesional

## Logros desde la última reunión
*No se mencionan específicamente*

## Logros no reportados
*No se mencionan específicamente*

## Bloqueos reportados
- Equipos de desarrollo solicitan ayuda en temas que deberían ser de su responsabilidad (ej. Dockerfile)
- MVPs que se mantienen sin evolucionar generan riesgos operacionales

## Desafíos actuales
- Definición clara de responsabilidades entre equipos
- Gestión de riesgos operacionales derivados de MVPs no evolucionados
- Estrategias adecuadas para exposición de APIs

## Bienestar y equilibrio trabajo-vida
*No se menciona específicamente en esta reunión*

## Feedback bidireccional
### Observaciones sobre Javier
- Muestra preocupación por la cultura técnica de la empresa
- Tiene claridad sobre las responsabilidades que deberían corresponder a cada equipo
- Conocimiento detallado sobre APIs, Cloud Run, Apigee y autenticación externa

### Feedback para la organización
- Necesidad de clarificar responsabilidades entre equipos de desarrollo y DevOps
- Preocupación por la cultura de mantener MVPs sin evolucionar para escenarios de escala
- Posible necesidad de capacitación para desarrolladores en temas de DevOps

## Plan de Carrera (Observaciones / acciones)
- Propuesta para que identifique áreas específicas donde necesita apoyo para crecimiento personal/profesional

## Métricas de crecimiento
*No se establecieron métricas específicas en esta reunión*

## Recursos de aprendizaje recomendados
*No se discutieron específicamente en esta reunión*

## Alineación con valores de la empresa
*No se discutió específicamente en esta reunión*

## Objetivos para la próxima reunión
- Revisar áreas identificadas para crecimiento personal/profesional
- Discutir posible plan de capacitación para desarrolladores

## Acuerdos y acciones
| Acción | Responsable | Fecha límite | Prioridad |
|--------|-------------|--------------|-----------|
| Analizar puntos que necesiten apoyo para crecimiento personal/profesional | Javier | Próxima reunión | Media |
| Explorar posibilidades de plan de capacitación para desarrolladores | Tech Lead | Próxima reunión | Media |

## Notas adicionales
Esta reunión se centró en las preocupaciones de Javier sobre aspectos culturales y técnicos de la empresa. Expresó su inquietud respecto a la falta de claridad en las responsabilidades entre equipos, específicamente mencionando que los desarrolladores suelen solicitar ayuda en tareas que deberían ser de su responsabilidad, como la creación de Dockerfiles. También compartió observaciones sobre las APIs de la empresa, comparando Cloud Run vs Apigee, y discutiendo estrategias de autenticación externa. Un punto importante fue su preocupación sobre la práctica de mantener MVPs sin evolucionar para escenarios de escala, lo que genera riesgos operacionales. Como acción, se le propuso identificar áreas específicas donde necesite apoyo para su crecimiento personal/profesional, mientras que el Tech Lead explorará la posibilidad de implementar un plan de capacitación para desarrolladores en temas de DevOps.
