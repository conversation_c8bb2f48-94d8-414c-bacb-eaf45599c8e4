# Plan de Capacitación Security (AppSec) - <PERSON><PERSON><PERSON>

## Información General

- **Participante**: <PERSON><PERSON><PERSON>
- **Objetivo**: Preparación para certificación CSSLP (Certified Secure Software Lifecycle Professional)
- **Duración**: 12 sesiones de 1 hora cada una (sesión semanal + autoestudio)
- **Fecha de inicio**: Mayo 2025
- **Primera revisión**: 16 de mayo, 2025 (primer módulo)

## Descripción del Programa

El programa cubre los 8 dominios del Cuerpo de Conocimiento Común (CBK) de CSSLP, con énfasis en la aplicación práctica de conceptos de seguridad en todo el ciclo de vida del desarrollo de software.

## Objetivos de Aprendizaje

Al finalizar este programa, Nicolás será capaz de:

- Comprender y aplicar los principios fundamentales de seguridad en el software
- Integrar prácticas de seguridad en cada fase del ciclo de vida del desarrollo
- Identificar y mitigar riesgos de seguridad en aplicaciones
- Diseñar arquitecturas de software seguras
- Implementar prácticas de codificación segura
- Realizar pruebas de seguridad efectivas
- Gestionar la seguridad durante el despliegue y operaciones
- Evaluar y mitigar riesgos en la cadena de suministro de software
- Prepararse adecuadamente para el examen de certificación CSSLP

## Requisitos Previos

- Conocimientos básicos de desarrollo de software
- Familiaridad con conceptos fundamentales de seguridad informática
- Experiencia práctica en alguna fase del ciclo de vida del desarrollo de software
- Acceso a un entorno de desarrollo para ejercicios prácticos

## Calendario de Sesiones

### Sesión 1: Introducción y Conceptos Fundamentales de Seguridad
- Presentación del programa y estructura del examen CSSLP
- Conceptos fundamentales: confidencialidad, integridad, disponibilidad
- Autenticación, autorización, responsabilidad y no repudio
- Estándares de gobernanza, riesgo y cumplimiento (GRC)

### Sesión 2: Principios de Diseño Seguro
- Principios de mínimo privilegio y segregación de funciones
- Defensa en profundidad y resiliencia
- Economía de mecanismo y mediación completa
- Diseño abierto y aceptabilidad psicológica
- Modelos de control de acceso (RBAC, DAC, MAC)

### Sesión 3: Gestión del Ciclo de Vida de Software Seguro
- Integración de seguridad en metodologías de desarrollo
- Estándares de seguridad y marcos de referencia
- Estrategia y hoja de ruta de seguridad
- Documentación de seguridad y métricas
- Desmantelamiento de aplicaciones
- Gestión de riesgos integrada

### Sesión 4: Requerimientos de Software Seguro (Parte 1)
- Definición de requisitos de seguridad funcionales y no funcionales
- Requisitos de cumplimiento regulatorio y legal
- Requisitos de clasificación de datos
- Ciclo de vida de los datos y manejo de datos sensibles

### Sesión 5: Requerimientos de Software Seguro (Parte 2)
- Requisitos de privacidad y protección de datos
- Aprovisionamiento de acceso a datos
- Desarrollo de casos de mal uso y abuso
- Matriz de trazabilidad de requisitos de seguridad
- Requisitos de seguridad para proveedores externos

### Sesión 6: Arquitectura de Seguridad y Modelado de Amenazas
- Definición de arquitectura de seguridad
- Patrones de arquitectura y diseño seguro
- Arquitecturas específicas (distribuida, SOA, IoT, Cloud, Mobile)
- Modelado de amenazas (STRIDE, PASTA, CVSS)
- Evaluación de superficie de ataque

### Sesión 7: Diseño de Interfaces y Tecnologías Seguras
- Diseño de interfaces seguras
- Evaluación y selección de tecnologías reutilizables
- Gestión de credenciales y control de flujo
- Evaluación de riesgos arquitectónicos
- Arquitectura operativa segura

### Sesión 8: Implementación de Software Seguro (Parte 1)
- Prácticas de codificación segura
- Validación de entrada y sanitización de salida
- Manejo de errores y excepciones
- Registro y auditoría seguros
- Gestión de sesiones y control de acceso
- Implementación de controles criptográficos

### Sesión 9: Implementación de Software Seguro (Parte 2)
- Análisis de código para riesgos de seguridad
- Herramientas de análisis estático (SAST)
- Implementación de controles de seguridad
- Integración segura de componentes de terceros
- Seguridad en el proceso de compilación

### Sesión 10: Pruebas de Software Seguro
- Estrategia y plan de pruebas de seguridad
- Desarrollo de casos de prueba de seguridad
- Pruebas automatizadas (DAST, IAST)
- Pruebas de penetración y fuzzing
- Verificación de documentación
- Clasificación y seguimiento de errores
- Gestión de datos de prueba seguros

### Sesión 11: Despliegue, Operaciones y Mantenimiento
- Análisis de riesgos operacionales
- Configuración segura y control de versiones
- Liberación segura de software (CI/CD)
- Gestión de datos de seguridad
- Monitoreo continuo y respuesta a incidentes
- Gestión de parches y vulnerabilidades
- Protección en tiempo de ejecución
- Continuidad de operaciones

### Sesión 12: Cadena de Suministro de Software Seguro y Preparación para el Examen
- Gestión de riesgos de la cadena de suministro
- Análisis de seguridad de software de terceros
- Verificación de pedigrí y procedencia
- Requisitos contractuales de seguridad
- Estrategias para el examen CSSLP
- Simulacro de examen y revisión final

## Metodología de Enseñanza
- Autoestudio del material entregado
- Sesiones interactivas con discusiones
- Casos de estudio y ejemplos reales
- Ejercicios prácticos y proyectos

## Recursos Principales
- CSSLP All-in-One Exam Guide (Daniel Carter)
- OWASP Application Security Verification Standard (ASVS)
- NIST Special Publications (800-series)
- ISO/IEC 27034: Application Security

## Recursos Complementarios
- OWASP Software Assurance Maturity Model (SAMM)
- Building Security in Maturity Model (BSIMM)
- Herramientas de modelado de amenazas (Microsoft Threat Modeling Tool, OWASP Threat Dragon)

## Cronograma de Evaluaciones
- **16 de mayo, 2025**: Revisión del primer módulo
- Evaluaciones periódicas después de cada módulo
- Simulacros de examen reducidos a lo largo del programa

## Conexión con Objetivos Q2
Este plan de capacitación se alinea directamente con el objetivo personal de Nicolás para Q2 2025: "Completar capacitación interna en SDLC orientada a AppSec y aprobar el examen de prueba al final de la capacitación interna, basado en el estándar CSSLP antes del fin del Q2 de forma de quedar en condiciones de Rendir examen oficial CSSLP desde el próximo trimestre."

## Notas de Seguimiento
*Esta sección se actualizará con notas sobre el progreso de Nicolás a lo largo del programa.*
