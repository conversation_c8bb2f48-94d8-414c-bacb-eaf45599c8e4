# Inicio de Capacitación: System Design y Arquitectura - 24/04/2025

## Información básica
- **Miembro del equipo**: <PERSON>
- **<PERSON><PERSON>**: 24/04/2025
- **Tipo de actividad**: Inicio de capacitación
- **<PERSON><PERSON> clave**: System Design, Arquitectura, Desarrollo profesional
- **Relación con plan de carrera**: Desarrollo de habilidades arquitecturales para progresión a Senior

## Detalles de la capacitación

Se ha iniciado el programa de capacitación en "System Design y Arquitectura de Sistemas" para Tobias <PERSON> y <PERSON>. Este programa se alinea con el plan de desarrollo profesional de Tobias, específicamente con su interés en arquitectura de software y su progresión planificada a Senior en Q2 2025.

### Estructura del programa

- **Duración**: 24 semanas (aproximadamente 6 meses)
- **Formato**: Entrega de material una vez por semana
- **Metodología**: Sesiones teórico-prácticas con accionables concretos
- **Evaluación**: Accionables semanales, seguimiento mensual, revisiones por fase, proyecto final integrador

### Contenido general

El programa está estructurado en tres fases:

1. **Fundamentos de System Design** (Semanas 1-8)
   - Conceptos básicos, características no funcionales, sistemas distribuidos, dimensionamiento, DNS, load balancers, caching, bases de datos

2. **Componentes y Patrones de Arquitectura** (Semanas 9-16)
   - Microservicios, mensajería, patrones de diseño, APIs, autenticación, testing, monitoreo, seguridad

3. **Implementación de Casos Prácticos** (Semanas 17-24)
   - Arquitecturas serverless, Kubernetes, escalabilidad, circuit breaking, alta disponibilidad, big data, SRE

### Conexión con intereses previos

Esta capacitación se conecta directamente con varios intereses y actividades previas de Tobias:

- Su trabajo con diagramas C4 (discutido en reuniones del 25/03 y 07/04)
- Su interés en arquitectura expresado consistentemente en reuniones anteriores
- Las discusiones técnicas sobre multitenant y autenticación (reunión del 22/04)
- Su propuesta sobre niveles de madurez de microservicios (reunión del 22/04)

## Próximos pasos

- Seguimiento de la primera sesión y accionable asignado
- Coordinación con Joel para posibles sesiones de discusión conjunta
- Integración de los temas de la capacitación con proyectos actuales cuando sea posible

## Notas adicionales

El plan completo de capacitación se encuentra documentado en `1on1_meetings/capacitacion/plan_system_design_arquitectura.md` para referencia y seguimiento.
