# 1:1 con <PERSON> - 27/02/2025

## Información básica
- **Miembro del equipo**: <PERSON>
- **Fecha**: 27/02/2025
- **Duración**: 60 minutos (16:00 - 17:00)
- **Ciclo de revisión**: Regular
- **<PERSON><PERSON> clave**: Técnico, Infraestructura, <PERSON><PERSON><PERSON>, Bienestar
- **Nivel de satisfacción**: 3 - Buena
- **Seguimiento requerido**: Sí
- **Relación con OKRs**: Mejora de procesos DevOps y comunicación interdepartamental

## Seguimiento de acciones previas
*No se mencionan acciones específicas de la reunión anterior*

## Temas tratados
- Estado técnico actual y carga de trabajo
- Situación como único DevOps (cloud) en el equipo
- Preferencias técnicas: Security y Networking vs. Monitorización
- Gestión de tiempos y agendas de proyectos por parte de los PM
- Experiencia con Cloud Run y GitHub Actions
- Necesidad de inventarios de recursos/proyectos cloud
- Propuesta de cambios organizacionales para mejorar comunicación entre áreas
- Estructuración de logs de aplicaciones

## Logros desde la última reunión
*No se mencionan específicamente*

## Logros no reportados
*No se mencionan específicamente*

## Bloqueos reportados
- Falta de apoyo en el área de DevOps
- Deficiencias en la gestión de tiempos y agendas por parte de los PM
- Falta de inventarios de recursos/proyectos cloud

## Desafíos actuales
- Gestionar toda el área de DevOps sin apoyo adicional
- Riesgo de burnout identificado por el Tech Lead
- Necesidad de mejorar la comunicación entre áreas

## Bienestar y equilibrio trabajo-vida
- Posible riesgo de burnout (identificado por el Tech Lead)
- Sobrecarga por ser el único DevOps

## Feedback bidireccional
### Observaciones sobre Javier
- Muestra preferencias claras: disfruta Security, Networking, Cloud Run y GitHub Actions
- Le aburren los temas de monitorización
- Tiene visión para mejoras organizacionales

### Feedback para la organización
- Falta mejor manejo en los tiempos y agendas de los proyectos por parte de los PM
- Necesidad de implementar Cambios Organizacionales para mejorar comunicación entre áreas (ej. Request For Changes)
- Necesidad de estructurar los logs de aplicaciones

## Plan de Carrera (Observaciones / acciones)
*No se discutió específicamente en esta reunión*

## Métricas de crecimiento
*No se establecieron métricas específicas en esta reunión*

## Recursos de aprendizaje recomendados
*No se discutieron específicamente en esta reunión*

## Alineación con valores de la empresa
- Su preocupación por mejorar la comunicación entre áreas demuestra visión organizacional
- Su interés en seguridad refleja compromiso con la calidad y protección de datos

## Objetivos para la próxima reunión
- Revisar avances en la búsqueda de apoyo para el área de DevOps
- Discutir implementación de propuestas de mejora organizacional
- Evaluar progreso en la creación de inventarios de recursos cloud

## Acuerdos y acciones
| Acción | Responsable | Fecha límite | Prioridad |
|--------|-------------|--------------|-----------|
| Explorar opciones para incorporar apoyo en DevOps | Tech Lead | Próximas semanas | Alta |
| Iniciar creación de inventarios de recursos/proyectos cloud | Javier | A definir | Media |
| Elaborar propuesta formal de Request For Changes | Javier | Próxima reunión | Media |
| Desarrollar plan para estructuración de logs de aplicaciones | Javier | A definir | Media |

## Notas adicionales
Esta reunión reveló varios puntos de atención importantes. El Tech Lead identificó un posible riesgo de burnout, dado que Javier es actualmente el único responsable de DevOps (cloud) y siente que le falta apoyo. Tiene preferencias técnicas claras, disfrutando áreas como Security, Networking, Cloud Run y GitHub Actions, mientras que los temas de monitorización le resultan menos interesantes. Proporcionó feedback valioso sobre la gestión de proyectos, señalando deficiencias en el manejo de tiempos y agendas por parte de los PM. También identificó necesidades organizacionales como la implementación de procesos formales (Request For Changes) para mejorar la comunicación entre áreas, la creación de inventarios de recursos cloud, y la estructuración de logs de aplicaciones. Estas observaciones demuestran una visión que va más allá de sus responsabilidades inmediatas, pero también subrayan la presión que podría estar experimentando al ser el único responsable de un área crítica.
