# Objetivos Q2 2025 - <PERSON><PERSON><PERSON>

## Período
- **Inicio**: 29 de abril, 2025
- **Fin**: 30 de junio, 2025

## Objetivos de Proyectos (Synapse) - 70%

### 1. Expansión operativa del sistema - 35%
- **Descripción**: Liberar al menos una nueva funcionalidad con valor real al cliente por sprint (cada 2 semanas), en el marco de la expansión operativa del sistema en los meses de mayo y junio.
- **Fecha límite**: 30 de junio, 2025
- **Métricas de éxito**:
  - Mínimo 4 funcionalidades liberadas (1 por sprint)
  - Cada funcionalidad debe aportar valor real al cliente
  - Documentación adecuada de cada funcionalidad

### 2. Integración del primer agente de IA a Synapse - 35%
- **Descripción**: Diseñar e implementar la arquitectura base necesaria para interactuar/integrar el primer agente de IA en Synapse, con una versión funcional en demo al final del Q2.
- **Fecha límite**: 30 de junio, 2025
- **Métricas de éxito**:
  - Arquitectura base implementada
  - Integración funcional con al menos un agente de IA
  - Demo funcional al final del Q2

### 3. Primera entrega a cliente interno - 30%
- **Descripción**: Entregar una versión funcional del módulo de Plan de cuidado de pacientes crónicos a final de Q2.
- **Fecha límite**: 30 de junio, 2025
- **Métricas de éxito**:
  - Módulo funcional entregado
  - Aceptación por parte del cliente interno
  - Documentación completa del módulo

## Objetivos de Impacto Transversal y Colaboración - 15%

### 1. Implementar proceso de prácticas de seguridad en el SDLC - 100%
- **Descripción**: Implementar una versión básica del proceso de prácticas de seguridad en el SDLC para Synapse, documentada y aplicada al menos en 2 entregas durante el Q2.
- **Fecha límite**: 30 de junio, 2025
- **Métricas de éxito**:
  - Proceso de prácticas de seguridad documentado
  - Implementación en al menos 2 entregas
  - Documentación de resultados y lecciones aprendidas
  - Recomendaciones para expansión del proceso

## Objetivos Personales - 15%

### 1. Capacitación interna en SDLC orientada a AppSec - 100%
- **Descripción**: Completar capacitación interna en SDLC orientada a AppSec y aprobar el examen de prueba al final de la capacitación interna, basado en el estándar CSSLP antes del fin del Q2 de forma de quedar en condiciones de Rendir examen oficial CSSLP desde el próximo trimestre.
- **Fecha límite**: 30 de junio, 2025
- **Métricas de éxito**:
  - Capacitación completada
  - Examen de prueba aprobado
  - Preparación completa para examen oficial CSSLP
  - Aplicación de conocimientos en el proceso de seguridad SDLC

## Plan de seguimiento

### Reuniones de seguimiento
- Reuniones 1:1 regulares para revisar progreso
- Revisión quincenal de objetivos de proyecto
- Revisión mensual de objetivos de seguridad y personales

### Documentación de progreso
- Registro de funcionalidades liberadas
- Documentación del proceso de seguridad SDLC
- Seguimiento de implementaciones de seguridad
- Avance en capacitación CSSLP

## Conexión con plan de desarrollo

Estos objetivos se alinean perfectamente con el plan de desarrollo de Nicolás en varios aspectos:

1. **Especialización en seguridad**: Tanto el objetivo transversal como el personal están enfocados en seguridad, alineándose con el compromiso de enfocar su carrera hacia seguridad (reunión del 10/04/2025).

2. **Aplicación práctica**: La implementación del proceso de seguridad en el SDLC proporciona una oportunidad inmediata para aplicar conocimientos de seguridad en un contexto real.

3. **Preparación para certificación**: La capacitación orientada al estándar CSSLP establece un camino claro hacia una certificación reconocida en seguridad de aplicaciones.

4. **Equilibrio entre proyecto y especialización**: Mantiene sus responsabilidades en el proyecto Synapse mientras desarrolla su especialización en seguridad.

## Notas adicionales

Estos objetivos han sido diseñados considerando:

- La decisión reciente de Nicolás de enfocar su desarrollo profesional hacia seguridad
- Su rol como Staff Developer con capacidad para liderar iniciativas transversales
- La necesidad de mejorar la calidad del trabajo (mencionada en la reunión del 10/04/2025)
- La finalización reciente de las migraciones de servicios (solo restaban 2 servicios Core)

La combinación de objetivos de proyecto con iniciativas de seguridad crea un plan que permite a Nicolás mantener sus responsabilidades actuales mientras desarrolla su especialización en un área estratégica para la organización. La implementación de prácticas de seguridad en el SDLC también aborda directamente su preocupación por mejorar la calidad del trabajo.
