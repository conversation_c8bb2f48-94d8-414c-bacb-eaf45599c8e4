# Análisis de Evolución del Equipo - Febrero a Mayo 2025

## Análisis General por Integrante

### 1. **CRISTIAN MURUA** - Evolución Anímica y Profesional

#### **Progresión Anímica** 📈📉
- **Feb 26**: **Nivel 4** - <PERSON><PERSON> buena disposición, entusiasta con nueva estructura
- **Feb 27**: **Nivel 4** - Mantiene energía positiva, enfocado en transición
- **Mar 17**: **Nivel 3** - Buena, pero aparecen primeras dudas sobre crecimiento
- **Mar 26**: **Nivel 4** - <PERSON><PERSON> buena, motivado con curso FHIR
- **Abr 08**: **🚨 Nivel 2** - **CRISIS VOCACIONAL** - Considerando renunciar, problemas de salud
- **May 07**: **Nivel 3** - Recuperación gradual, interés en IA
- **May 28**: **Preparación** - Enfoque en compensación y progresión

#### **Crecimiento Profesional**
- **Técnico**: De soporte frontend/backend básico → Desarrollo end-to-end en Synapse
- **FHIR**: Curso 90% completado (objetivo personal Q2)
- **Mentoría**: Plan intensivo 6 semanas con Tobias (50% progreso)
- **IA**: Nuevo interés desarrollado, alineado con "agent space"
- **Autonomía**: Mejora gradual en estimaciones y entrega de tareas

#### **Aspectos Relevantes**
- **Crisis de Abril**: Punto de inflexión crítico - salud física → impacto anímico → crisis vocacional
- **Recuperación**: Apoyo estructurado (curso FHIR + mentoría Tobias) fue clave
- **Evolución**: De inseguridad técnica → interés específico en IA
- **Patrón**: Responde muy bien a estructura y apoyo directo

### 2. **MICAELA PERCOVICH** - Evolución Anímica y Profesional

#### **Progresión Anímica** 📈
- **Feb 26**: **Nivel 4** - Muy buena, pero con inseguridad técnica
- **Feb 27**: **Nivel 4** - Mantiene disposición positiva
- **Mar 18**: **Nivel 4** - Creciente confianza
- **Mar 28**: **Nivel 4** - Entusiasta con certificación GCP
- **Abr 08**: **Nivel 4** - Sin problemas de salud, estable
- **May 07**: **Nivel 4** - **CRECIMIENTO EN LIDERAZGO** reconocido
- **May 28**: **Preparación** - Confiada, enfoque en time management

#### **Crecimiento Profesional**
- **Liderazgo**: De "falta de confianza" → Reconocida como "traductora" DevOps
- **DevSecOps**: Certificación GCP Professional Cloud DevOps Engineer (20% progreso)
- **Capacitación**: Liderando workshops interequipos (35% progreso)
- **Ownership**: Crecimiento notable en iniciativa y responsabilidad
- **Actividad DEV25**: Demostración exitosa de habilidades de facilitación

#### **Aspectos Relevantes**
- **Evolución Constante**: Trayectoria ascendente sin crisis significativas
- **Rol Estratégico**: Se convirtió en puente entre desarrollo y DevOps
- **Liderazgo Natural**: Emergió como líder sin imposición formal
- **Balance**: Maneja bien múltiples responsabilidades (trabajo, estudio, familia)

### 3. **JAVIER SANKOWICZ** - Evolución Anímica y Profesional

#### **Progresión Anímica** 📉⚠️
- **Feb 25**: **Nivel 4** - Muy buena disposición inicial
- **Feb 27**: **Nivel 4** - Mantiene energía positiva
- **Mar 17**: **Nivel 3** - Primeras señales de carga
- **Mar 27**: **🚨 Nivel 2** - **SOBRECARGA EVIDENTE** - Desánimo visible
- **Abr 10**: **Nivel 3** - Ligera mejora con apoyo
- **May 09**: **Nivel 3** - Estable pero no resuelto completamente
- **May 28**: **Preparación** - **FRUSTRACIÓN TÉCNICA** evidente

#### **Crecimiento Profesional**
- **Seguridad**: Implementación OWASP Top 10 (logro significativo)
- **CI/CD**: Avances en cloud deploy con Micaela
- **Sonarqube**: Implementación exitosa en pipelines
- **Coordinación**: Mejora gradual en priorización con apoyo del Tech Lead

#### **Aspectos Relevantes**
- **Patrón de Burnout**: Sobrecarga → desánimo → frustración técnica
- **Dependencias**: Frustra por depender de desarrolladores para implementar security
- **Coordinación**: Falta de alineación arquitectónica genera estrés
- **Vacaciones**: Planificadas estratégicamente (Jun 15-25) para recuperación

---

## Talking Points para Reuniones 1:1

### 🎯 **CRISTIAN MURUA** (28/05 - 11:00h)

#### **Temas Prioritarios**
1. **Compensación y Progresión** (tema principal de su preparación)
2. **Evaluación de crecimiento técnico**
3. **Continuidad del plan de desarrollo**

#### **Talking Points**

**1. Reconocimiento de Progreso**
- "Cris, he visto un crecimiento significativo desde abril. Tu recuperación y enfoque en IA es notable"
- "El progreso en FHIR (90%) y la colaboración con Tobi muestran tu compromiso"
- "Los desarrollos end-to-end que mencionas demuestran evolución técnica real"

**2. Evaluación de Seniority**
- "Revisemos juntos los criterios de progresión Junior → Semi Senior"
- "Tus logros actuales vs. criterios del plan de carrera Q2 2025"
- "¿Qué evidencias concretas tienes de autonomía en features completas?"

**3. Compensación**
- "Entiendo tu preocupación salarial. Hablemos de cronograma realista"
- "La progresión salarial está ligada a la progresión de seniority"
- "¿Qué expectativas específicas tienes y en qué timeframe?"

**4. Plan de Desarrollo Continuo**
- "Tu interés en IA se alinea perfectamente con los objetivos de Synapse"
- "¿Cómo podemos estructurar tu desarrollo en 'agent space'?"
- "Continuidad del plan con Tobi - ¿qué ajustes necesitas?"

#### **Accionables y Tareas Pendientes**

| Acción | Responsable | Fecha Límite | Estado |
|--------|-------------|--------------|--------|
| Evaluar progreso contra criterios Semi Senior | Mauricio | 30/05 | ⏳ Pendiente |
| Revisar estructura salarial con RRHH | Mauricio | 05/06 | ⏳ Pendiente |
| Definir plan específico desarrollo IA | Cristian/Mauricio | 05/06 | ⏳ Pendiente |
| Documentar evidencias de autonomía técnica | Cristian | 02/06 | ⏳ Pendiente |
| Seguimiento plan intensivo con Tobi | Tobias | Continuo | ✅ En progreso |

---

### 🎯 **MICAELA PERCOVICH** (28/05 - 14:30h)

#### **Temas Prioritarios**
1. **Time Management y Priorización**
2. **Reconocimiento de liderazgo emergente**
3. **Progreso en objetivos Q2**

#### **Talking Points**

**1. Reconocimiento de Crecimiento**
- "Mica, tu evolución en liderazgo ha sido excepcional"
- "Tu rol como 'traductora' DevOps es estratégico para la organización"
- "La actividad DEV25 demostró habilidades de facilitación maduras"

**2. Time Management**
- "Entiendo el desafío de balancear 6h estudio + 6h capacitación + 28h Synapse"
- "¿Qué estrategias específicas podemos implementar para mejorar tu priorización?"
- "¿La distribución actual refleja realmente las prioridades organizacionales?"

**3. Progreso Objetivos Q2**
- "Capacitación interequipos 35% - ¿qué necesitas para acelerar?"
- "GCP 20% - ¿el ritmo actual te permitirá llegar a la certificación?"
- "Synapse 50% - ¿estás satisfecha con el progreso del equipo?"

**4. Desarrollo Futuro**
- "¿Cómo ves tu evolución hacia roles de mayor liderazgo técnico?"
- "¿Qué tipo de responsabilidades adicionales te motivarían?"

#### **Accionables y Tareas Pendientes**

| Acción | Responsable | Fecha Límite | Estado |
|--------|-------------|--------------|--------|
| Desarrollar estrategia time management personalizada | Mauricio/Micaela | 02/06 | ⏳ Pendiente |
| Revisar distribución de tiempo semanal | Micaela | 30/05 | ⏳ Pendiente |
| Evaluar progreso certificación GCP | Micaela | 05/06 | ⏳ Pendiente |
| Planificar próximas capacitaciones interequipos | Micaela | 05/06 | ⏳ Pendiente |
| Definir métricas de éxito para rol "traductora" | Mauricio | 02/06 | ⏳ Pendiente |

---

### 🎯 **JAVIER SANKOWICZ** (28/05 - 16:30h)

#### **Temas Prioritarios**
1. **🚨 Frustración técnica y coordinación**
2. **Gestión de dependencias y prioridades**
3. **Preparación para vacaciones y reducción de carga**

#### **Talking Points**

**1. Reconocimiento y Validación**
- "Javi, tu trabajo en seguridad (OWASP Top 10) es fundamental y valioso"
- "Entiendo tu frustración con las dependencias para implementar security"
- "Tu expertise es crítico - necesitamos encontrar formas de potenciarlo"

**2. Coordinación y Arquitectura**
- "La falta de coordinación en arquitectura cloud es un problema real"
- "¿Qué estructura de comunicación necesitas para estar en el loop?"
- "¿Cómo podemos formalizar tu participación en decisiones arquitectónicas?"

**3. Gestión de Dependencias**
- "El tema de priorización de security vs. desarrollo es organizacional"
- "¿Qué mecanismos necesitas para que security sea realmente prioritario?"
- "¿Cómo podemos reducir tu dependencia de desarrolladores para implementaciones?"

**4. Logging y Monitoring**
- "Tu interés en ordenar logging y monitoring es estratégico"
- "¿Qué apoyo específico necesitas para liderar esta iniciativa?"
- "¿Cómo podemos asegurar que no se dupliquen esfuerzos?"

**5. Vacaciones y Transición**
- "Confirmación: puedes reducir carga cliente Perú desde ahora, salida completa 1 junio"
- "Vacaciones 15-25 junio aprobadas - ¿qué cobertura necesitas?"

#### **Accionables y Tareas Pendientes**

| Acción | Responsable | Fecha Límite | Estado |
|--------|-------------|--------------|--------|
| Comunicar salida cliente Perú (confirmado por CTO) | Mauricio | 29/05 | ⏳ Pendiente |
| Definir estructura participación en decisiones arquitectónicas | Mauricio | 05/06 | ⏳ Pendiente |
| Crear proceso priorización security vs. desarrollo | Mauricio/Javier | 10/06 | ⏳ Pendiente |
| Planificar iniciativa logging y monitoring | Javier | 05/06 | ⏳ Pendiente |
| Organizar cobertura vacaciones junio | Mauricio/Javier | 10/06 | ⏳ Pendiente |
| Revisar objetivos Q2 (no completados en preparación) | Javier | 30/05 | ⏳ Pendiente |

#### **Señales de Alerta para Monitorear**
- Nivel de frustración técnica
- Signos de burnout o desánimo
- Efectividad de las medidas de apoyo implementadas
- Balance trabajo-vida durante preparación de vacaciones

---

## Observaciones Estratégicas

### **Patrones Identificados**
1. **Cristian**: Responde muy bien a estructura y apoyo directo
2. **Micaela**: Crecimiento constante, necesita desafíos de liderazgo
3. **Javier**: Riesgo de burnout, necesita autonomía y reconocimiento técnico

### **Acciones Organizacionales Requeridas**
1. **Revisión de criterios de progresión salarial** (Cristian, Nicolás)
2. **Estructura de coordinación arquitectónica** (Javier)
3. **Formalización de roles de liderazgo emergente** (Micaela)
4. **Distribución de conocimiento DevOps** (Javier → Micaela)

---

*Documento generado: 28/05/2025*
*Análisis basado en reuniones 1:1 desde febrero 2025*
*Próxima actualización: Post reuniones 1:1 de mayo 2025*
