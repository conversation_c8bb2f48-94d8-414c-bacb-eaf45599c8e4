# Objetivos Q2 2025 - <PERSON><PERSON><PERSON>

## Período
- **Inicio**: 29 de abril, 2025
- **Fin**: 30 de junio, 2025

## Objetivos de Proyectos (Synapse) - 70%

### 1. Expansión operativa del sistema - 35%
- **Descripción**: Liberar al menos una nueva funcionalidad con valor real al cliente por sprint (cada 2 semanas), en el marco de la expansión operativa del sistema en los meses de mayo y junio.
- **Fecha límite**: 30 de junio, 2025
- **Métricas de éxito**:
  - Mínimo 4 funcionalidades liberadas (1 por sprint)
  - Cada funcionalidad debe aportar valor real al cliente
  - Documentación adecuada de cada funcionalidad

### 2. Integración del primer agente de IA a Synapse - 35%
- **Descripción**: Diseñar e implementar la arquitectura base necesaria para interactuar/integrar el primer agente de IA en Synapse, con una versión funcional en demo al final del Q2.
- **Fecha límite**: 30 de junio, 2025
- **Métricas de éxito**:
  - Arquitectura base implementada
  - Integración funcional con al menos un agente de IA
  - Demo funcional al final del Q2

### 3. Primera entrega a cliente interno - 30%
- **Descripción**: Entregar una versión funcional del módulo de Plan de cuidado de pacientes crónicos a final de Q2.
- **Fecha límite**: 30 de junio, 2025
- **Métricas de éxito**:
  - Módulo funcional entregado
  - Aceptación por parte del cliente interno
  - Documentación completa del módulo

## Objetivos de Impacto Transversal y Colaboración - 15%

### 1. Capacitación DevSecOp Interequipos UMA - 100%
- **Descripción**: Realizar, al menos, 3 capacitaciones técnicas (temas propuestos: Dockerfiles, revisión de Logs y Monitorización Cloud/Deploy) dirigidas a equipos fuera de Proyecto Synapse, con enfoque práctico y documentos de apoyo entregados.
- **Fecha límite**: 30 de junio, 2025
- **Métricas de éxito**:
  - 3 capacitaciones técnicas completadas
  - Documentos de apoyo entregados para cada capacitación
  - Feedback positivo de los participantes
  - Aumento de autonomía en equipos capacitados

## Objetivos Personales - 15%

### 1. Capacitación Track DevOps/GCP - 100%
- **Descripción**: Completar capacitación en DevSecOps, aprobar evaluaciones relacionadas con ≥80% y estar en condiciones de rendir la certificación oficial Google Cloud Certification del track seleccionado a inicios del próximo trimestre.
- **Fecha límite**: 30 de junio, 2025
- **Métricas de éxito**:
  - Capacitación completada
  - Evaluaciones aprobadas con ≥80%
  - Preparación completa para certificación oficial
  - Aplicación de conocimientos en proyectos actuales

## Plan de seguimiento

### Reuniones de seguimiento
- Reuniones 1:1 regulares para revisar progreso
- Revisión quincenal de objetivos de proyecto
- Revisión mensual de objetivos de capacitación y personales

### Documentación de progreso
- Registro de funcionalidades liberadas
- Seguimiento de capacitaciones realizadas
- Documentación de avance en track DevOps/GCP
- Feedback de participantes en capacitaciones

## Conexión con plan de desarrollo

Estos objetivos se alinean con el plan de desarrollo de Micaela en varios aspectos:

1. **Desarrollo en DevSecOps**: La capacitación en DevOps/GCP y las capacitaciones interequipos se alinean directamente con su elección de la certificación Google Cloud Professional Cloud DevOps Engineer (reunión del 28/03/2025).

2. **Liderazgo técnico**: Las capacitaciones interequipos le permiten desarrollar habilidades de facilitación y liderazgo técnico, expandiendo su impacto más allá de su equipo inmediato.

3. **Autonomía de equipos**: Su iniciativa de planificar workshops para autonomía en DevOps (mencionada en la reunión del 08/04/2025) se formaliza como un objetivo oficial.

4. **Colaboración con Javier**: Estos objetivos complementan su colaboración existente con Javier en temas de DevOps y pueden ayudar a distribuir conocimiento, aliviando parte de la carga de Javier.

## Notas adicionales

Estos objetivos han sido diseñados considerando:

- El interés de Micaela en DevSecOps y su elección de la certificación Google Cloud Professional Cloud DevOps Engineer
- Su iniciativa de planificar workshops para equipos de desarrollo
- La necesidad organizacional de distribuir conocimiento de DevOps más allá de Javier
- Su capacidad para balancear responsabilidades de proyecto con iniciativas transversales

La combinación de objetivos de proyecto, impacto transversal y desarrollo personal crea un plan equilibrado que aprovecha sus fortalezas mientras continúa su crecimiento profesional.
