# 1:1 con <PERSON> - 30/06/2025

## Información básica
- **Miembro del equipo**: <PERSON>
- **<PERSON><PERSON>**: 30/06/2025
- **Duración**: 72 minutos (15:16 - 16:28)
- **Ciclo de revisión**: Regular + Cierre Q2
- **Temas clave**: Programa Agentes, Liderazgo, Frustración, Evaluación, Promoción a Senior
- **Nivel de satisfacción**: 3 - <PERSON><PERSON> (frustración por programa agentes, satisfacción por reconocimiento)
- **Seguimiento requerido**: Sí
- **Relación con OKRs**: Evaluación Q2 + Planificación carrera Senior

## Seguimiento de acciones previas
- Participación en capacitación System Design & Arquitectura: En progreso
- Proyecto RIMAC: Completado con honores
- Programa de agentes: Bloqueado por falta de participación del equipo
- Career path: 6 entregables pendientes

## Temas tratados
Prioridad: Alta
- **Crisis en programa de agentes**: Frustración extrema por falta de participación del equipo
- **Liderazgo fallido**: Experiencia negativa liderando equipo de 5-6 personas
- **Evaluación excepcional**: Reconocimiento de nivel técnico y promoción a Senior
- **Desarrollo profesional**: Blog técnico, portfolio, especialización en IA/Agentes
- **Gestión de equipos**: Lecciones aprendidas sobre liderazgo y gestión de recursos
- **Arquitectura empresarial**: Introducción a TOGAF y certificaciones
- **Coordinación técnica**: Reconocimiento por liderazgo en reunión RIMAC-Synapse

## Logros desde la última reunión
- **RIMAC completado**: Proyecto finalizado "con honores" y reconocimiento técnico
- **Implementación/POCs con RAG**: Avances técnicos en arquitecturas con IA
- **Profundización técnica**: Estudio de consistencia, comunicación, fault tolerance, post-mortem
- **Liderazgo técnico**: Reconocimiento por coordinación exitosa entre equipos RIMAC y Synapse
- **Nivel técnico excepcional**: Evaluación como referente técnico en UMA

## Logros no reportados
- **Capacidad arquitectural**: Desarrollo de soluciones integrales únicas a nivel mundial
- **Mentoría técnica**: Apoyo efectivo a otros desarrolladores en tareas complejas
- **Visión de producto**: Comprensión de necesidades de documentación y comunicación técnica
- **Resiliencia técnica**: Capacidad de resolver problemas complejos sin experiencia previa

## Bloqueos reportados
- **Programa de agentes**: Equipo completamente desmotivado, sin participación en 4 semanas
- **Sobrecarga de roles**: PM + Arquitecto + Desarrollador + QA en un solo proyecto
- **Falta de apoyo**: Equipo no responde, no participa, no tiene iniciativa
- **Gestión de tiempo**: Dificultad para manejar múltiples responsabilidades

## Desafíos actuales
- **Decisión sobre programa agentes**: Evaluar continuidad vs abandono del proyecto
- **Gestión de frustración**: Evitar que afecte otras responsabilidades
- **Desarrollo de liderazgo**: Aprender a exigir y gestionar equipos efectivamente
- **Balance de carga**: Distribución adecuada de responsabilidades
- **Especialización técnica**: Enfoque en arquitectura e IA/Agentes

## Bienestar y equilibrio trabajo-vida
- **Salud**: Recuperándose de gripe de 2 semanas
- **Frustración alta**: Programa agentes afectando motivación general
- **Satisfacción técnica**: Alta por reconocimiento y proyectos exitosos
- **Carga de trabajo**: Bien balanceada fuera del programa agentes
- **Motivación**: Mixta - alta en proyectos técnicos, baja en gestión de equipos

## Feedback bidireccional
### Observaciones sobre Joel
- **Nivel técnico excepcional**: "Tienes un nivel muy bueno, eres un referente en UMA"
- **Capacidad de resolución**: "Capacidad de resolver cosas técnicas sin experiencia previa"
- **Liderazgo técnico**: "Me dio mucha alegría verte liderando la conversación entre equipos"
- **Promoción merecida**: "Para mí hay que pasarte a Senior, eres un referente"
- **Potencial arquitectural**: "Ahí se supone que salen tú y Toby como arquitectos"
- **Retención de talento**: "Si me fuera a otro lado, estás dentro de las personas que me llevaría"

### Feedback para el líder
- **Gestión de recursos**: Necesidad de mejor seguimiento y exigencia a equipos
- **Apoyo en crisis**: Valoración del apoyo para resolver situación del programa agentes
- **Desarrollo profesional**: Apreciación por planificación de carrera y certificaciones

## Plan de Carrera (Observaciones / acciones)
- **Promoción a Senior**: Confirmada para este semestre, reconocimiento de nivel técnico
- **Especialización en Arquitectura**: Preparación para Staff Engineer con foco en arquitectura
- **Certificaciones planificadas**: 
  - Google Cloud Architect (requerida para Staff)
  - TOGAF (Arquitectura Empresarial)
- **Desarrollo de blog técnico**: Objetivo de una publicación mensual
- **Portfolio profesional**: Creación con NextJS y Sanity CMS

## Métricas de crecimiento
| Habilidad/Área | Estado actual | Objetivo | Progreso |
|----------------|---------------|----------|----------|
| Nivel técnico | Senior | Senior | 100% |
| Arquitectura | Semi Senior+ | Senior | 90% |
| Liderazgo técnico | Semi Senior | Senior | 85% |
| Gestión de equipos | Junior | Semi Senior | 40% |
| Especialización IA/Agentes | Semi Senior+ | Senior | 80% |

## Recursos de aprendizaje recomendados
- **TOGAF Certification**: Arquitectura empresarial para próximo año
- **Google Cloud Architect**: Certificación requerida para Staff
- **Blog técnico**: Desarrollo de contenido mensual
- **Libros de sistemas distribuidos**: Continuación de estudio en seguridad y SLA
- **Post-mortem practices**: Implementación de cultura de análisis

## Alineación con valores de la empresa
- **Excelencia técnica**: Demostración consistente de alto nivel técnico
- **Innovación**: Desarrollo de soluciones únicas a nivel mundial
- **Colaboración**: Liderazgo efectivo en coordinación inter-equipos
- **Crecimiento**: Evolución acelerada hacia roles de mayor responsabilidad

## Objetivos para la próxima reunión
- Resolución definitiva sobre programa de agentes
- Evaluación formal de promoción a Senior
- Planificación detallada de certificaciones
- Seguimiento de desarrollo de blog técnico
- Revisión de carga de trabajo post-promoción

## Acuerdos y acciones
| Acción | Responsable | Fecha límite | Prioridad |
|--------|-------------|--------------|-----------|
| Conversación con Diana sobre programa agentes | Joel | 02/07/2025 | Alta |
| Evaluación formal de promoción | Mauricio | 05/07/2025 | Alta |
| Inicio de blog técnico | Joel | 31/07/2025 | Media |
| Investigación TOGAF certification | Mauricio | 15/07/2025 | Media |
| Planificación de certificaciones Q4 | Mauricio/Joel | 01/08/2025 | Media |

## Notas adicionales
Esta reunión de 72 minutos fue intensa y reveladora, mostrando tanto el alto nivel técnico de Joel como los desafíos significativos en gestión de equipos. El Tech Lead fue explícito sobre la promoción a Senior y el potencial futuro como arquitecto.

**Programa de Agentes - Crisis de Liderazgo**: Joel expresó frustración extrema con el programa de agentes donde lidera un equipo de 5-6 personas que no participan activamente. Después de 6 semanas, Joel está haciendo todo el trabajo (PM, Arquitecto, Desarrollador, QA) mientras el equipo no contribuye. La situación llegó al punto donde Joel considera abandonar el programa.

**Lecciones de Liderazgo**: El Tech Lead compartió experiencias valiosas sobre gestión de equipos, enfatizando que "ser líder es como ser entrenador de fútbol" - cuando el equipo gana, los jugadores son los héroes; cuando pierde, el entrenador es responsable. La importancia de exigir rendimiento equitativo y no permitir que los buenos elementos carguen con el trabajo de los demás.

**Reconocimiento Técnico Excepcional**: El Tech Lead fue categórico: "Para mí hay que pasarte a Senior, eres un referente en UMA". Joel demostró capacidad de resolver problemas técnicos complejos sin experiencia previa, especialmente en el proyecto RIMAC que fue calificado como "único en el mundo".

**Coordinación Inter-equipos**: Reconocimiento específico por el liderazgo mostrado en la reunión entre equipos RIMAC y Synapse: "Me dio mucha alegría verte a ti y a Toby liderando esta conversación, el nivel fue bastante bueno y fue conducido excelentemente".

**Desarrollo Profesional - Blog Técnico**: Joel expresó interés en crear un blog técnico para documentar proyectos complejos y decisiones arquitecturales. El Tech Lead sugirió que UMA debería tener un blog técnico corporativo donde los referentes se posicionen externamente.

**Arquitectura Empresarial**: Introducción a TOGAF como certificación clave para arquitectos. El Tech Lead enfatizó que "con TOGAF certification, ya puedes decir que eres un arquitecto" y planificó capacitación formal para el próximo año.

**Gestión de Recursos y Tiempo**: Discusión profunda sobre la importancia de que los líderes gestionen el tiempo y recursos, no los desarrolladores. "El tiempo no lo tienen que gestionar los desarrolladores, es tarea del TL gestionar el tiempo y los recursos".

**Retención de Talento**: El Tech Lead fue explícito sobre el valor de Joel: "Si me fuera a otro lado, créeme que estás dentro de las personas que yo me llevaría". Esto refleja el alto nivel de confianza y reconocimiento.

**Promoción Confirmada**: La promoción a Senior está confirmada para este semestre, con planificación hacia Staff Engineer especializado en arquitectura para el próximo año.

**Frustración Gestionada**: Aunque Joel mostró frustración significativa con el programa agentes, el Tech Lead ayudó a contextualizar la experiencia como aprendizaje valioso sobre liderazgo y gestión de equipos.

**Visión de Futuro**: Planificación clara hacia especialización en arquitectura con certificaciones Google Cloud Architect y TOGAF, posicionando a Joel como uno de los futuros arquitectos de UMA junto con Tobias.

La reunión concluyó con un fuerte reconocimiento del crecimiento y potencial de Joel, balanceando la frustración actual con una visión clara de desarrollo profesional hacia roles de mayor responsabilidad técnica.
