Puesto: Senior Developer

Seniority: Senior

Level: 7,8 y 9 

Responsabilidades:
- Diseñar e implementar features complejas de forma autónoma
- Liderar técnicamente proyectos de alcance medio
- Realizar code reviews exhaustivos y de alta calidad
- Mentorizar activamente a D1 y D2
- Participar significativamente en decisiones de arquitectura
- Identificar y abordar problemas sistémicos
- Contribuir a la mejora continua de procesos de desarrollo
- Colaborar efectivamente con otros departamentos
- Evaluar y mitigar riesgos técnicos
- Define y monitorea métricas clave en sistemas y procesos. Usa datos para justificar mejoras técnicas.
- Adaptarse a cambios en la arquitectura y procesos organizacionales, promoviendo soluciones escalables y sostenibles
- Garantizar que las soluciones técnicas tienen un impacto positivo en la experiencia del usuario final
- Tomar decisiones técnicas alineadas con las necesidades del usuario y los objetivos

Funciones Principales:
Innovación
• Diseña soluciones técnicas con un enfoque en escalabilidad y sostenibilidad a largo plazo.
• Introduce tecnologías emergentes y evalúa su impacto en la organización.
• Promueve una cultura de experimentación y aprendizaje dentro del equipo.
Alineación con los OKRs de la empresa
• Diseña soluciones que escalan con el crecimiento del negocio y mejoran la experiencia del usuario.
• Reduce deuda técnica asegurando estabilidad y facilidad de mantenimiento.
• Lidera la implementación de estrategias de monitoreo y métricas clave para evaluar impacto técnico.
Ejemplos de Comportamientos:
• Ejemplo positivo: Identifica y aborda deuda técnica proactivamente.
• Ejemplo positivo: Mentora a ingenieros junior estructuradamente.
• Ejemplo positivo: Se adapta rápidamente a nuevas prioridades del negocio sin comprometer la calidad técnica.
• Ejemplo positivo: Propone mejoras técnicas basadas en feedback de usuarios o métricas de uso del sistema
• Área de desarrollo: Puede enfocarse demasiado en perfección técnica vs. entrega.

Skills técnicas mínimas esperadas:
DevSecOps y Entrega Continua
• Diseño de flujos CI/CD complejos (build optimizado, rollback, automatizaciones)
• Orquestación de contenedores (Kubernetes básico o equivalente)
• Definición de SLOs, alertas proactivas y observabilidad avanzada
• Estrategias de testing a escala (TDD, e2e, performance)
• Implementación de prácticas DevSecOps (escaneo de vulnerabilidades, hardening de imágenes)

Diseño y Arquitectura de Sistemas
• Diseño de subsistemas independientes y escalables
• Aplicación de patrones arquitectónicos (CQRS, Event Sourcing, Clean Architecture)
• Diseño y mantenimiento de APIs distribuidas y versionadas
• Arquitectura distribuida (clustering, mensajes asincrónicos, resiliencia)
• Diseño avanzado para optimización de performance (caching, load balancing)
• Diseño de arquitectura de sistemas de IA

Seguridad, Gobernanza y Gestión de Datos
• Diseño de arquitecturas seguras (zero trust, authentication flows avanzados)
• Gestión activa de riesgos de seguridad a nivel de sistemas
• Planificación de compliance y preparación para auditorías regulatorias
• Gobierno de datos a gran escala (data lineage, data quality, privacidad)
• Definición de estrategias éticas en IA (fairness, seguridad de modelos)

Innovación y Estrategia Técnica
• Diseño y liderazgo de iniciativas de innovación tecnológica (IA aplicada, nuevas arquitecturas)
• Análisis estratégico de impacto técnico en negocio
• Liderazgo en gestión del conocimiento dentro del equipo y la organización
• Creación de estrategias tecnológicas de mediano y largo plazo
• Participación activa en investigación de nuevas tendencias disruptivas (IA, cloud-native, plataformas emergentes)

Formación académica adicional:
Formación académica completa o equivalente en experiencia
Se valora título universitario (Ing. en Sistemas, Lic. en Informática, etc.), pero no es excluyente si hay trayectoria sólida

Requisitos de experiencia: Exp tipica: 4 a 6 años

Soft Skills requeridas:
Colaboración & Comunicación
Comprensión de las necesidades del cliente
Adaptabilidad & Agilidad
Capacidad Analítica - Data-Centric Thinking
Empatía
Innovación
Pensamiento Crítico
Autonomía e Impacto
Liderazgo & Mentoring

Comportamientos esperados por cada soft skills de acuerdo al Seniority:
1. Comunicación y Colaboración
- Articula conversaciones complejas.
- Facilita acuerdos entre partes.
- Influye positivamente en la cultura colaborativa.
- Facilita discusiones técnicas y traduce conceptos complejos para audiencias no técnicas.

2. Autonomía e Impacto
- Actúa con autonomía estratégica.
- Tiene alto impacto en resultados clave.
- Influye en la dirección del equipo o área.
- Opera independientemente y genera impacto en múltiples áreas del proyecto. 

3. Adaptabilidad y Agilidad
- Lidera la adaptación del equipo.
- Anticipa cambios y se adelanta con propuestas.
- Promueve una cultura ágil y flexible.
- Guía cambios en procesos y adopción de nuevas tecnologías. 

4. Capacidad analítica - Data Centric
- Integra múltiples fuentes de datos para la toma de decisiones estratégicas.
- Promueve una cultura data-driven en el equipo.
- Realiza análisis profundos de sistemas complejos y métricas.

5. Comprensión de la necesidad del Cliente/Usuario
- Anticipa necesidades del cliente.
- Influye en decisiones de negocio.
- Es referente en la relación con el cliente/usuario.
- Traduce objetivos de negocio en soluciones técnicas. 

6. Empatía
- Es un modelo de empatía.
- Maneja situaciones complejas con sensibilidad.
- Inspira un ambiente inclusivo y de confianza.
- Anticipa necesidades de usuarios y otros desarrolladores.

7. Innovación
- Promueve una cultura de innovación.
- Detecta oportunidades disruptivas.
- Lidera iniciativas de transformación.
- Desarrolla nuevos enfoques para problemas recurrentes. 

8. Pensamiento Crítico
- Desafía constructivamente. 
- Toma decisiones complejas. 
- Promueve un pensamiento estratégico en el equipo.
- Evalúa ventajas y desventajas técnicas a largo plazo. 

9. Liderazgo y Mentoring
- Actúa como referente técnico o funcional dentro del equipo.
- Comparte buenas prácticas y conocimientos con pares y perfiles más junior.
- Brinda soporte informal a colegas que lo consultan, demostrando predisposición a ayudar.
- Inspira desde el ejemplo, mostrando compromiso y responsabilidad.

Proximo Nivel:
(IC) Staff Engineer
(Management) Tech Lead



