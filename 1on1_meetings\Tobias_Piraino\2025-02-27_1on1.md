# 1:1 con <PERSON> - 27/02/2025

## Información básica
- **Miembro del equipo**: <PERSON>
- **<PERSON><PERSON>**: 27/02/2025
- **Duración**: 60 minutos (13:00 - 14:00)
- **Ciclo de revisión**: Regular
- **<PERSON><PERSON> clave**: Técnico, Proyecto, Carrera
- **Nivel de satisfacción**: 4 - <PERSON><PERSON> buena
- **Seguimiento requerido**: Sí
- **Relación con OKRs**: Desarrollo de arquitecturas y capacitación interna

## Seguimiento de acciones previas
*No se mencionan acciones específicas de la reunión anterior*

## Temas tratados
- Estado técnico de los proyectos actuales
- Interés en certificación GCP Cloud
- Preferencias de metodologías de trabajo (Kanban vs Scrum)
- Capacitaciones internas realizadas
- Arquitecturas propuestas, especialmente para autenticación en proyecto Synapse

## Logros desde la última reunión
- Propuesta de arquitectura de autenticación para proyecto Synapse (implementada)
- Realización de capacitaciones internas sobre microservicios y uso de Git
- Capacitación sobre herramientas de debug en entornos NodeJS

## Logros no reportados
*No se mencionan específicamente*

## Bloqueos reportados
*No se reportan bloqueos específicos*

## Desafíos actuales
- Debilidades en el equipo respecto a microservicios y uso de Git
- Uso limitado de herramientas de debug en entornos NodeJS

## Bienestar y equilibrio trabajo-vida
*No se menciona específicamente en esta reunión*

## Feedback bidireccional
### Observaciones sobre Tobias
- Demuestra claridad y capacidad de resolución (sus arquitecturas propuestas son las que se están implementando)
- Muestra honestidad y transparencia al hablar de sí mismo y su trabajo
- Proactivo en identificar áreas de mejora y proporcionar capacitación
- Preferencia por metodologías Kanban sobre Scrum

### Feedback para el líder
*No se menciona feedback específico*

## Plan de Carrera (Observaciones / acciones)
- Interés en obtener certificación GCP Cloud
- Fortalezas en diseño de arquitecturas y capacitación

## Métricas de crecimiento
*No se establecieron métricas específicas en esta reunión*

## Recursos de aprendizaje recomendados
*No se discutieron específicamente, aunque se menciona interés en certificación GCP*

## Alineación con valores de la empresa
- Su iniciativa para realizar capacitaciones internas demuestra compromiso con el crecimiento del equipo
- Su honestidad y transparencia reflejan valores de comunicación abierta

## Objetivos para la próxima reunión
- Explorar opciones concretas para certificación GCP Cloud
- Revisar el impacto de las capacitaciones realizadas
- Discutir posibles mejoras en la adopción de herramientas de debug

## Acuerdos y acciones
| Acción | Responsable | Fecha límite | Prioridad |
|--------|-------------|--------------|-----------|
| Investigar opciones de certificación GCP Cloud | Tobias | Próxima reunión | Media |
| Evaluar el impacto de las capacitaciones realizadas | Tech Lead | Próxima reunión | Media |

## Notas adicionales
Esta reunión tuvo un enfoque técnico, destacando las fortalezas de Tobias en el diseño de arquitecturas y su contribución a través de capacitaciones internas. Su honestidad y transparencia son valores positivos, aunque se observa la importancia de asegurar que esta franqueza no afecte negativamente al hablar del trabajo de otros en el futuro. Su preferencia por Kanban sobre Scrum es consistente con lo mencionado en la reunión anterior sobre su incomodidad con metodologías muy estructuradas. Su interés en la certificación GCP Cloud sugiere una dirección para su desarrollo profesional.
