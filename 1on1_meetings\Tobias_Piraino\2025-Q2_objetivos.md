# Objetivos Q2 2025 - <PERSON>

## Período
- **Inicio**: 29 de abril, 2025
- **Fin**: 30 de junio, 2025

## Objetivos de Proyectos (Synapse) - 70%

### 1. Expansión operativa del sistema - 35%
- **Descripción**: Liberar al menos una nueva funcionalidad con valor real al cliente por sprint (cada 2 semanas), en el marco de la expansión operativa del sistema en los meses de mayo y junio.
- **Fecha límite**: 30 de junio, 2025
- **Métricas de éxito**:
  - Mínimo 4 funcionalidades liberadas (1 por sprint)
  - Cada funcionalidad debe aportar valor real al cliente
  - Documentación adecuada de cada funcionalidad

### 2. Integración del primer agente de IA a Synapse - 35%
- **Descripción**: Diseñar e implementar la arquitectura base necesaria para interactuar/integrar el primer agente de IA en Synapse, con una versión funcional en demo al final del Q2.
- **Fecha límite**: 30 de junio, 2025
- **Métricas de éxito**:
  - Arquitectura base implementada
  - Integración funcional con al menos un agente de IA
  - Demo funcional al final del Q2

### 3. Primera entrega a cliente interno - 30%
- **Descripción**: Entregar una versión funcional del módulo de Plan de cuidado de pacientes crónicos a final de Q2.
- **Fecha límite**: 30 de junio, 2025
- **Métricas de éxito**:
  - Módulo funcional entregado
  - Aceptación por parte del cliente interno
  - Documentación completa del módulo

## Objetivos de Impacto Transversal y Colaboración - 15%

### 1. Acompañamiento técnico a miembros del equipo - 50%
- **Descripción**: Mentorizar técnicamente a 1 miembro del equipo con menor experiencia con seguimiento semanal o quincenal.
- **Fecha límite**: 30 de junio, 2025
- **Métricas de éxito**:
  - Sesiones regulares de mentoría (semanales o quincenales)
  - Documentación de temas tratados y progreso
  - Feedback del miembro mentorizado
  - Mejora demostrable en habilidades técnicas del mentorizado

### 2. Liderar iniciativas de mejora en la arquitectura de Synapse - 50%
- **Descripción**: Liderar, al menos, una iniciativa de mejora en la arquitectura de Synapse (ej. refactor core, definición de nuevas capas o patrones), con impacto medible y entregable técnico documentado.
- **Fecha límite**: 30 de junio, 2025
- **Métricas de éxito**:
  - Iniciativa de mejora arquitectural implementada
  - Documentación técnica completa (posiblemente usando diagramas C4)
  - Impacto medible en calidad, mantenibilidad o rendimiento
  - Presentación de la mejora al equipo

## Objetivos Personales - 15%

### 1. Capacitación interna System Design / Arquitectura - 100%
- **Descripción**: Completar capacitación interna en System Design y aprobar evaluación interna de los contenidos revisados y vistos con ≥80% al finalizar la semana 12 del Q2.
- **Fecha límite**: 30 de junio, 2025
- **Métricas de éxito**:
  - Capacitación completada
  - Evaluación aprobada con ≥80%
  - Aplicación de conceptos en iniciativas de arquitectura
  - Documentación de aprendizajes clave

## Plan de seguimiento

### Reuniones de seguimiento
- Reuniones 1:1 regulares para revisar progreso
- Revisión quincenal de objetivos de proyecto
- Seguimiento de sesiones de mentoría
- Revisión de avances en iniciativa arquitectural

### Documentación de progreso
- Registro de funcionalidades liberadas
- Documentación de sesiones de mentoría
- Avances en iniciativa arquitectural
- Progreso en capacitación de System Design

## Conexión con plan de desarrollo

Estos objetivos se alinean perfectamente con el plan de desarrollo de Tobias en varios aspectos:

1. **Progresión a Senior**: Los objetivos de liderazgo técnico y mentoría apoyan directamente su progresión planificada a Senior en Q2 2025 (evaluado como "Senior Minus" en la reunión del 25/03/2025).

2. **Enfoque en arquitectura**: La iniciativa de mejora arquitectural y la capacitación en System Design se alinean con su interés expresado en arquitectura de software y su trabajo previo con diagramas C4.

3. **Desarrollo de liderazgo técnico**: La mentoría a un miembro con menor experiencia desarrolla sus habilidades de liderazgo sin gestión directa de personas.

4. **Aplicación práctica de conocimientos**: La iniciativa arquitectural proporciona un espacio para aplicar los conceptos aprendidos en la capacitación de System Design.

## Notas adicionales

Estos objetivos han sido diseñados considerando:

- El interés de Tobias en arquitectura de software (expresado consistentemente en reuniones anteriores)
- Su trabajo previo con diagramas C4 (reuniones del 25/03 y 07/04/2025)
- Su evaluación como "Senior Minus" con progresión planificada a Senior en Q2 2025
- Su participación en la capacitación de System Design & Arquitectura iniciada el 24/04/2025
- Su capacidad para balancear responsabilidades académicas y laborales (mencionada el 11/04/2025)

La combinación de objetivos de proyecto, impacto transversal y desarrollo personal crea un plan equilibrado que aprovecha sus fortalezas en arquitectura y diseño de sistemas, mientras desarrolla habilidades de liderazgo técnico necesarias para su progresión a Senior.
