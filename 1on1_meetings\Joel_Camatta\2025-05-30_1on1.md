# 1:1 con <PERSON> - 30/05/2025

## Información básica
- **Miembro del equipo**: <PERSON>
- **<PERSON><PERSON>**: 30/05/2025
- **Duración**: 46 minutos (12:33 - 13:19)
- **Ciclo de revisión**: Regular
- **<PERSON><PERSON> clave**: Liderazgo, Programa Agentes, Gestión Tiempo, Carrera, Proyectos
- **Nivel de satisfacción**: 4 - <PERSON><PERSON> buena
- **Seguimiento requerido**: Sí
- **Relación con OKRs**: Objetivos Q2 - Career path, programa agentes, capacitación arquitectura

## Seguimiento de acciones previas
- Participación en capacitación System Design & Arquitectura (en progreso)
- Desarrollo de career path y programa de agentes (acelerado por programa agentes)
- Trabajo en proyecto Rimac (finalizando en 2 meses)

## Temas tratados
- Experiencia exitosa liderando equipo en programa de agentes
- Desafío doble: técnico y de liderazgo con equipo de 5-6 personas
- Frustración con proyecto Rimac por simplicidad y decisiones arquitecturales
- Problemas de salud recurrentes (gripe) afectando rendimiento
- Gestión del tiempo entre múltiples responsabilidades
- Mesa de trabajo con Javi y Tobias para coordinación técnica
- Futuros proyectos y rotación de equipos
- Técnicas de gestión de calendario y protección de tiempo

## Logros desde la última reunión
- **Liderazgo exitoso**: Coordinación efectiva de equipo competitivo en programa agentes
- **Visión arquitectural**: Desarrollo de solución integral conectando múltiples componentes
- **Colaboración técnica**: Trabajo productivo con Javi en temas de Sonar y métricas
- **Capacitación arquitectura**: Progreso positivo en track con Tobias
- **Mentoría recibida**: Aplicación de consejos de Henry sobre delegación

## Logros no reportados
- Experiencia previa en arquitectura distribuida aplicada en Rimac
- Desarrollo de workshops y conocimiento en sistemas complejos
- Capacidad de identificar problemas de monitorización y trazabilidad
- Colaboración efectiva con Facu en visión holística de procesos

## Bloqueos reportados
- **Salud recurrente**: Gripe que afecta gimnasio, sueño y duplica esfuerzo laboral
- **Proyecto Rimac**: Tareas muy simples, decisiones arquitecturales no compartidas
- **Gestión del tiempo**: Dificultad para balancear 3 responsabilidades simultáneas
- **Falta de desafío**: Tareas estimadas en 8 días completadas en 2-3 días

## Desafíos actuales
- **Liderazgo de equipo**: Aprender a delegar y no hacer todo personalmente
- **Balance de responsabilidades**: Programa agentes + Rimac + capacitación arquitectura
- **Transición de rol**: De individual contributor a rol con componente de management
- **Salud**: Necesidad de atención médica para problemas respiratorios recurrentes

## Bienestar y equilibrio trabajo-vida
- **Estado de salud**: Gripe recurrente, cansancio extremo, necesita dormir 10 horas
- **Impacto en actividades**: No puede ir al gimnasio, problemas de sueño
- **Rendimiento laboral**: Trabajo requiere doble esfuerzo por estado de salud
- **Motivación**: Alta con programa agentes, baja con proyecto Rimac
- **Perspectiva médica**: Programada visita al médico

## Feedback bidireccional
### Observaciones sobre Joel
- **Liderazgo natural**: "Tienes un desafío doble, técnico y de liderazgo"
- **Visión arquitectural**: Capacidad para ver conexiones holísticas en sistemas
- **Experiencia valiosa**: Conocimiento en Rimac será útil para futuros proyectos
- **Colaboración técnica**: Aportes significativos en temas de monitorización
- **Crecimiento evidente**: Evolución hacia roles de mayor responsabilidad

### Feedback para el líder
- **Apoyo en transición**: Reconocimiento del desafío de cambio de rol
- **Planificación futura**: Valoración de perspectivas sobre próximos proyectos
- **Técnicas de gestión**: Apreciación por compartir métodos de organización

## Plan de Carrera (Observaciones / acciones)
- **Transición a liderazgo**: Desarrollo natural hacia roles con componente de management
- **Especialización arquitectura**: Progreso en track con aplicación práctica
- **Rotación de proyectos**: Planificación para mover de Rimac a proyectos más desafiantes
- **Mesa de trabajo técnica**: Participación en coordinación con DevOps y arquitectura
- **Futuros proyectos**: Consideración para proyectos con clientes reales y mayor complejidad

## Métricas de crecimiento
| Habilidad/Área | Estado actual | Objetivo | Progreso |
|----------------|---------------|----------|----------|
| Liderazgo de equipo | En desarrollo | Senior | Progreso acelerado |
| Arquitectura de sistemas | En desarrollo | Senior | En track |
| Gestión del tiempo | Mejorando | Optimizar | En desarrollo |
| Colaboración técnica | Senior | Senior | Cumplido |
| Visión holística | Senior | Senior | Cumplido |

## Recursos de aprendizaje recomendados
- **Técnicas de delegación**: Aplicación de consejos de Henry sobre "dejar que lo hagan ellos"
- **Gestión de calendario**: Implementación de Focus Time y protección de horarios
- **Mesa de trabajo técnica**: Coordinación con Javi (DevOps) y Tobias (Arquitectura)
- **Objetivos estructurados**: Creación de objetivos 70% proyecto, 15% personal, 15% transversal
- **Atención médica**: Panel de alergias y evaluación respiratoria

## Alineación con valores de la empresa
- **Liderazgo colaborativo**: Desarrollo de habilidades de coordinación de equipos
- **Visión técnica**: Aplicación de conocimientos arquitecturales para mejoras organizacionales
- **Innovación**: Participación en programa de agentes con soluciones integrales
- **Crecimiento continuo**: Evolución hacia roles de mayor responsabilidad

## Objetivos para la próxima reunión
- Evaluar progreso en liderazgo del programa de agentes
- Revisar implementación de técnicas de gestión del tiempo
- Seguimiento de estado de salud y atención médica
- Planificación de transición post-Rimac
- Evaluación de participación en mesa de trabajo técnica

## Acuerdos y acciones
| Acción | Responsable | Fecha límite | Prioridad |
|--------|-------------|--------------|-----------|
| Implementar Focus Time en calendario con horarios protegidos | Joel | 05/06/2025 | Alta |
| Crear objetivos estructurados (70/15/15) con Henry | Joel | 10/06/2025 | Alta |
| Participar en mesa de trabajo con Javi y Tobias | Joel | 05/06/2025 | Media |
| Visita médica para evaluación respiratoria/alergias | Joel | 05/06/2025 | Alta |
| Planificar transición de Rimac a nuevo proyecto | Mauricio | 15/06/2025 | Media |
| Proteger horario de almuerzo en calendario | Joel | Inmediato | Media |
| Coordinar con Henry sobre objetivos y seguimiento | Mauricio | 05/06/2025 | Media |

## Notas adicionales
Esta reunión de 46 minutos fue muy productiva y marcó un punto de inflexión importante en el desarrollo de Joel hacia roles de liderazgo. El programa de agentes se ha convertido en un catalizador inesperado para su crecimiento profesional.

**Programa de Agentes**: Joel está liderando un equipo competitivo de 5-6 personas (Facu, Juan de Cuba, Joaquín) en desarrollo de solución integral de soporte con LLM. La experiencia le está enseñando delegación y coordinación, con mentoría valiosa de Henry sobre "dejar que lo hagan ellos" en lugar de hacer todo personalmente.

**Visión Arquitectural**: Demostró capacidad holística al identificar que los problemas (Jira, dailys, coordinación) tenían "un hilo conductor" y requerían "solución integral". Esta perspectiva fue reconocida como similar a la que habría tomado el Tech Lead, indicando madurez arquitectural.

**Frustración con Rimac**: Expresó claramente que las tareas son "muy sencillas" y que no comparte decisiones arquitecturales, sintiendo que "queda muy feo el asunto". Tareas estimadas en 8 días las completa en 2-3, indicando subutilización de capacidades.

**Colaboración Técnica**: Trabajo exitoso con Javi en implementación de Sonar y métricas, demostrando capacidad de coordinación entre tracks técnicos. Se programó mesa de trabajo formal para unificar visiones de desarrollo y DevOps.

**Salud Recurrente**: Problema persistente de gripes que afecta significativamente su calidad de vida y rendimiento. Se recomendó evaluación médica similar a la experiencia del Tech Lead con asma/alergias.

**Gestión del Tiempo**: Se compartieron técnicas detalladas de calendario con Focus Time, protección de horarios, y estructura de objetivos 70/15/15. Joel mostró interés en implementar estas metodologías.

**Futuros Proyectos**: Se mencionaron oportunidades en proyectos con clientes reales y posible trabajo con Rimac/Cybers donde su experiencia sería valiosa. El Tech Lead expresó intención de incluirlo en proyectos de arquitectura de agentes.

La reunión concluyó con Joel dirigiéndose a consulta médica, demostrando priorización de su bienestar. Su evolución hacia liderazgo técnico es evidente y el programa de agentes está acelerando este desarrollo de manera natural.
