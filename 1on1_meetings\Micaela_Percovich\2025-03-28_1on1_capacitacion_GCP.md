# 1:1 con <PERSON><PERSON><PERSON> (+ <PERSON>) - 28/03/2025

## Información básica
- **Miembro del equipo**: <PERSON><PERSON><PERSON>
- **Fecha**: 28/03/2025
- **Duración**: 25 minutos (11:30 - 11:55)
- **Ciclo de revisión**: Especial
- **<PERSON><PERSON> clave**: Capacitación, DevOps, GCP, Carrera
- **Nivel de satisfacción**: 4 - <PERSON><PERSON> buena
- **Seguimiento requerido**: Sí
- **Relación con OKRs**: Desarrollo de habilidades técnicas y apoyo a DevSecOps
- **Participantes adicionales**: <PERSON> (DevOps)

## Seguimiento de acciones previas
- Seguimiento de la reunión del 18/03/2025 donde Micaela expresó interés en certificaciones GCP

## Temas tratados
- Experiencia previa de Micaela en GCP
- Cursos realizados anteriormente por Micaela
- Opciones de certificación GCP para Micaela
- Contexto y objetivos de la capacitación
- Orientación de Javier basada en su experiencia
- Apoyo de Javier en el proceso de capacitación

## Logros desde la última reunión
*No se mencionan específicamente*

## Bloqueos reportados
*No se reportan bloqueos específicos*

## Desafíos actuales
- Selección de la certificación GCP más adecuada para los objetivos profesionales y organizacionales

## Bienestar y equilibrio trabajo-vida
*No se menciona específicamente en esta reunión*

## Feedback bidireccional
### Observaciones sobre Micaela
- Compartió su experiencia previa y cursos realizados
- Mostró interés en desarrollo profesional en DevSecOps
- Se decantó por la certificación Google Cloud Professional Cloud DevOps Engineer

### Feedback para el líder/organización
*No se menciona feedback específico*

## Plan de Carrera (Observaciones / acciones)
- Orientación hacia DevSecOps como área de desarrollo profesional
- Selección de certificación Google Cloud Professional Cloud DevOps Engineer
- Planificación de apoyo por parte de Javier durante el proceso

## Métricas de crecimiento
*No se establecieron métricas específicas en esta reunión*

## Recursos de aprendizaje recomendados
- Certificación Google Cloud Professional Cloud DevOps Engineer

## Alineación con valores de la empresa
*No se discutió específicamente en esta reunión*

## Objetivos para la próxima reunión
- Confirmar plan de estudios para la certificación
- Establecer cronograma de preparación
- Definir mecanismos de apoyo por parte de Javier

## Acuerdos y acciones
| Acción | Responsable | Fecha límite | Prioridad |
|--------|-------------|--------------|-----------|
| Formalizar selección de certificación Google Cloud Professional Cloud DevOps Engineer | Micaela | 1 semana | Alta |
| Establecer plan de estudios y cronograma | Micaela/Tech Lead | 2 semanas | Alta |
| Definir mecanismos específicos de apoyo | Javier/Tech Lead | 2 semanas | Media |

## Notas adicionales
Esta reunión especial se realizó con el objetivo de definir la mejor opción de capacitación en GCP para Micaela, con miras a fortalecer sus habilidades en DevSecOps para los próximos trimestres. Contó con la participación de Javier Sankowicz, quien aportó su experiencia y orientación en el tema.

Durante la reunión, Micaela compartió su experiencia previa y los cursos que ha realizado relacionados con GCP. El Tech Lead proporcionó el contexto y los objetivos organizacionales que se buscan con esta capacitación, mientras que Javier ofreció orientación basada en su experiencia en el campo.

Tras la discusión, Micaela se inclinó por la certificación Google Cloud Professional Cloud DevOps Engineer como la opción más alineada con sus intereses y con las necesidades de la organización. También se estableció que Javier proporcionará apoyo durante el proceso de capacitación, lo que representa una oportunidad de mentoring y transferencia de conocimiento dentro del equipo.

Esta decisión se alinea con el interés previamente expresado por Micaela en certificaciones GCP (reunión del 18/03/2025) y con su colaboración existente con Javier en temas de DevOps. También representa un paso estratégico para fortalecer las capacidades de DevSecOps en el equipo, especialmente considerando la carga de trabajo actual de Javier como único DevOps.
