# 1:1 con <PERSON> - 21/03/2025

## Información básica
- **Miembro del equipo**: <PERSON>
- **<PERSON><PERSON>**: 21/03/2025
- **Duración**: 45 minutos (12:15 - 13:00)
- **Ciclo de revisión**: Regular
- **<PERSON><PERSON> clave**: Carrera, Evaluación, Técnico, Personal
- **Nivel de satisfacción**: 4 - <PERSON><PERSON> buena
- **Seguimiento requerido**: Sí
- **Relación con plan de carrera**: Evaluación de seniority y definición de track de desarrollo

## Seguimiento de acciones previas
*No se mencionan acciones específicas de la reunión anterior*

## Temas tratados
- Evaluación de nivel de seniority actual
- Situación actual en el equipo de backend
- Experiencia previa con clientes
- Intereses de desarrollo profesional
- Aspiraciones a largo plazo
- Fortalezas y áreas de mejora

## Logros desde la última reunión
*No se mencionan específicamente*

## Bloqueos reportados
*No se reportan bloqueos específicos*

## Desafíos actuales
- Necesidad de "pulir" temas técnicos
- Desarrollar mayor seguridad al definir temas técnicos

## Bienestar y equilibrio trabajo-vida
- Se siente más tranquilo desde que fue liberado de tareas de interacción con clientes
- Aspiración a largo plazo de tener su propia empresa

## Feedback bidireccional
### Observaciones sobre Joel
- Toma ownership de sus tareas
- Muestra buenas habilidades sociales
- Ayuda a capacitar y diseña servicios
- Capaz de desarrollar sin documentación técnica completa
- Evaluado como un recurso valioso

### Feedback para el líder/organización
*No se menciona feedback específico*

## Plan de Carrera (Observaciones / acciones)
- Interés en el track de Individual Contributor (IC)
- Enfoque en diseño y arquitectura de sistemas
- Evaluado como Semi Senior con potencial para crecer a Senior en Q2 2025
- Reconoce que siempre hay que mejorar en habilidades sociales

## Áreas de mejora específicas
### Seguridad técnica
- Necesita desarrollar mayor confianza al definir temas técnicos
- Requiere "pulir" algunos temas técnicos específicos

### Sobreingeniería
*No se discutió específicamente en esta reunión*

### Agilidad
*No se discutió específicamente en esta reunión*

### Comunicación
- Considera que tiene buen nivel en habilidades sociales
- Reconoce que siempre hay espacio para mejorar

### Integración de IA
*No se discutió específicamente en esta reunión*

## Métricas de crecimiento
*No se establecieron métricas específicas en esta reunión*

## Recursos de aprendizaje recomendados
*No se discutieron específicamente en esta reunión*

## Objetivos para la próxima reunión
- Definir plan concreto para progresión a Senior en Q2 2025
- Identificar áreas técnicas específicas a "pulir"
- Explorar oportunidades para desarrollo en diseño y arquitectura de sistemas

## Acuerdos y acciones
| Acción | Responsable | Fecha límite | Prioridad |
|--------|-------------|--------------|-----------|
| Desarrollar plan para progresión a Senior | Mauricio | Q2 2025 | Alta |
| Incorporar a Joel en capacitación interna de System Design & Arquitectura de Sistemas junto con Tobias | Mauricio/CTO | Q2 2025 | Alta |
| Identificar áreas técnicas específicas a mejorar | Joel/Mauricio | Próxima reunión | Media |
| Explorar oportunidades para desarrollo en diseño y arquitectura | Mauricio | Próxima reunión | Media |

## Puntos para compartir con Santiago
- Evaluación de Joel como Semi Senior con potencial para Senior en Q2 2025
- Interés de Joel en diseño y arquitectura de sistemas
- Necesidad de desarrollar mayor seguridad en definiciones técnicas

## Notas adicionales
Joel ha sido reasignado completamente (100%) al equipo de backend del proyecto liderado por Santiago, ya no forma parte del equipo de IA/MLOps. Al inicio del proyecto, Joel asumió responsabilidades de interacción con clientes (conversaciones, acuerdos), pero fue liberado de estas tareas cuando Santiago se incorporó, lo que le ha permitido sentirse más tranquilo.

Identifica como área de mejora la necesidad de "pulir" temas técnicos y desarrollar mayor seguridad al definir aspectos técnicos. Muestra interés claro en el track de Individual Contributor, con enfoque en diseño y arquitectura de sistemas. Considera que tiene un buen nivel de habilidades sociales, aunque reconoce que "siempre se necesita y siempre hay que mejorar". A largo plazo, aspira a tener su propia empresa.

Se observa que Joel toma ownership de sus tareas, muestra buenas habilidades sociales, contribuye a la capacitación y diseño de servicios, y es capaz de desarrollar incluso cuando no dispone de documentación técnica completa. Basado en estas observaciones, se le evalúa como un Semi Senior sin problemas, con potencial para crecer a Senior en Q2 2025.

Tras esta reunión, en conversación con el CTO, se decidió incorporar a Joel junto con Tobias en una capacitación interna de System Design & Arquitectura de Sistemas durante gran parte del Q2 2025. Esta decisión se alinea perfectamente con el interés de Joel en diseño y arquitectura, y proporcionará una oportunidad de aprendizaje colaborativo con otro miembro del equipo con intereses similares.
