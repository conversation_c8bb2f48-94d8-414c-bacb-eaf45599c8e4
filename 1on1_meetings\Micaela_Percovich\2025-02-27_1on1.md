# 1:1 con Micaela - 27/02/2025

## Información básica
- **Miembro del equipo**: <PERSON><PERSON><PERSON>
- **Fecha**: 27/02/2025
- **Duración**: 45 minutos (15:15 - 16:00)
- **Ciclo de revisión**: Regular
- **<PERSON><PERSON> clave**: Técnico, Proyecto, Infraestructura
- **Nivel de satisfacción**: 4 - <PERSON><PERSON> buena
- **Seguimiento requerido**: Sí
- **Relación con OKRs**: Desarrollo de Synapse y mejora de procesos internos

## Seguimiento de acciones previas
*No se mencionan acciones específicas de la reunión anterior*

## Temas tratados
- Estado técnico actual y carga de trabajo
- Proyecto Synapse: objetivos, arquitectura y tecnologías
- Apoyo en liquidaciones de pago a otras áreas
- Comparativa entre Kubernetes y Cloud Run
- Colaboración con Javier en temas de DevOps
- Levantamiento de casos de uso de backend para proyecto Synapse

## Logros desde la última reunión
- Avances en el proyecto Synapse
- Apoyo en temas de DevOps
- Estudio comparativo entre Kubernetes y Cloud Run

## Logros no reportados
*No se mencionan específicamente*

## Bloqueos reportados
*No se reportan bloqueos específicos*

## Desafíos actuales
- Automatización pendiente de liquidaciones de pago
- Implementación pendiente de event-driven development en Synapse

## Bienestar y equilibrio trabajo-vida
- Balance de 30% monotonía y 70% desafíos en su trabajo, lo que lo hace interesante
- Nivel de carga actual: 60%

## Feedback bidireccional
### Observaciones sobre Micaela
- Demuestra conocimiento técnico detallado del proyecto Synapse
- Capacidad para apoyar en múltiples áreas (backend, DevOps, liquidaciones)
- Interés en tecnologías de infraestructura (Kubernetes vs Cloud Run)

### Feedback para el líder
*No se menciona feedback específico*

## Plan de Carrera (Observaciones / acciones)
- Actualmente en rol de Semi Senior Developer enfocada en backend
- Interés en infraestructura y DevOps

## Métricas de crecimiento
*No se establecieron métricas específicas en esta reunión*

## Recursos de aprendizaje recomendados
*No se discutieron específicamente en esta reunión*

## Alineación con valores de la empresa
- Su trabajo en Synapse contribuye a adelantarse a las necesidades de la industria
- Su interés en la interoperabilidad con estándar FHIR demuestra visión de futuro

## Objetivos para la próxima reunión
- Revisar avances en la automatización de liquidaciones de pago
- Discutir progreso en la implementación de event-driven development
- Evaluar resultados del estudio comparativo entre Kubernetes y Cloud Run

## Acuerdos y acciones
| Acción | Responsable | Fecha límite | Prioridad |
|--------|-------------|--------------|-----------|
| Desarrollar plan para automatización de liquidaciones | Micaela | Próxima reunión | Media |
| Avanzar en la implementación de event-driven development | Micaela | A definir | Media |

## Notas adicionales
Esta reunión se centró en el estado técnico actual de Micaela y su trabajo en el proyecto Synapse. El proyecto tiene como objetivo adelantarse a las necesidades de la industria y ser interoperable con el estándar FHIR. La arquitectura se basa en microservicios, con event-driven development pendiente de implementación. Micaela también apoya en liquidaciones de pago a otras áreas, con automatización pendiente. Está estudiando las diferencias entre Kubernetes y Cloud Run, y colaborando con Javier en temas de DevOps. Reporta un balance interesante entre monotonía (30%) y desafíos (70%) en su trabajo, con un nivel de carga actual del 60%, lo que sugiere capacidad para asumir responsabilidades adicionales.
