# 1:1 con Cristian - 26/02/2025

## Información básica
- **Miembro del equipo**: <PERSON><PERSON><PERSON>
- **Fecha**: 26/02/2025
- **Duración**: 45 minutos (10:00 - 10:45)
- **Ciclo de revisión**: Regular
- **<PERSON><PERSON> clave**: <PERSON><PERSON>, Personal, Feedback
- **Nivel de satisfacción**: 4 - <PERSON><PERSON> buena
- **Seguimiento requerido**: Sí
- **Relación con OKRs**: Desarrollo de talento y definición de niveles de seniority

## Seguimiento de acciones previas
*Primera reunión como Tech Lead*

## Temas tratados
- Presentación como nuevo Tech Lead
- Experiencia laboral previa y trayectoria de Cristian
- Proyectos actuales: Suscripciones y transición a Synapse
- Aspiraciones profesionales y desarrollo de carrera
- Experiencias previas con reuniones 1:1
- Autoevaluación de fortalezas y áreas de mejora

## Logros desde la última reunión
*Primera reunión formal*

## Logros no reportados
- Experiencia como ayudante de cátedra en Coderhouse
- Transición exitosa de carrera desde áreas administrativas/ventas hacia desarrollo

## Bloqueos reportados
- Dificultad para decir "no" y para delegar tareas
- Alto nivel de autocrítica que podría ser limitante
- Falta de seguimiento consistente en reuniones 1:1 anteriores
- Falta de estándares claros para niveles de seniority

## Desafíos actuales
- Transición entre proyectos (Suscripciones → Synapse)
- Desarrollo de habilidades para avanzar de Junior a Semi Senior
- Gestionar el alto nivel de autocrítica

## Bienestar y equilibrio trabajo-vida
- Se siente cómodo en la empresa
- Muestra aprecio por la filosofía de trabajo
- No reporta problemas específicos de equilibrio trabajo-vida

## Feedback bidireccional
### Observaciones sobre Cristian
- Muestra iniciativa y motivación para crecer profesionalmente
- Tiene experiencia diversa que puede aportar perspectivas valiosas
- Presenta dificultad para establecer límites (decir "no")
- Nivel de autocrítica potencialmente excesivo

### Feedback para el líder/organización
- Las reuniones 1:1 anteriores han estado muy espaciadas
- Falta de seguimiento consistente del feedback
- Necesidad de estandarizar los niveles de seniority

## Plan de Carrera (Observaciones / acciones)
- Aspiración de avanzar de Junior a Semi Senior
- Interés en roles de liderazgo a futuro
- Necesidad de desarrollar habilidades de delegación y establecimiento de límites

## Métricas de crecimiento
| Habilidad/Área | Estado actual | Objetivo | Progreso |
|----------------|---------------|----------|----------|
| Nivel técnico | Junior con aspiración a Semi Senior | Semi Senior | En progreso |
| Delegación | Dificultad para delegar | Mejorar capacidad de delegación | Por iniciar |
| Establecer límites | Dificultad para decir "no" | Desarrollar asertividad | Por iniciar |

## Recursos de aprendizaje recomendados
*No se discutieron específicamente en esta primera reunión*

## Alineación con valores de la empresa
- Expresa aprecio por la filosofía de trabajo de la empresa
- Muestra lealtad y cariño hacia la organización

## Objetivos para la próxima reunión
- Definir criterios claros para la progresión de Junior a Semi Senior
- Establecer plan de acción para mejorar habilidades de delegación y asertividad
- Revisar progreso en la transición al proyecto Synapse

## Acuerdos y acciones
| Acción | Responsable | Fecha límite | Prioridad |
|--------|-------------|--------------|-----------|
| Establecer reuniones 1:1 regulares con seguimiento consistente | Tech Lead | Inmediato | Alta |
| Explorar la definición de niveles de seniority estandarizados | Tech Lead | Próxima reunión | Media |
| Identificar recursos para mejorar habilidades de delegación y asertividad | Ambos | Próxima reunión | Media |

## Notas adicionales
Cristian tiene una trayectoria interesante, habiendo trabajado 8 años en áreas administrativas y de ventas antes de hacer una transición de carrera a través del bootcamp de Henry. Esta experiencia diversa puede ser valiosa para el equipo. Su alto nivel de autocrítica, aunque demuestra compromiso con la mejora continua, podría convertirse en un obstáculo si no se gestiona adecuadamente. Muestra potencial de liderazgo pero necesita desarrollar habilidades complementarias como la delegación y el establecimiento de límites.
