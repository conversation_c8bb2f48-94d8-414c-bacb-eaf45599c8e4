# Objetivos Q2 2025 - <PERSON>

## Período
- **Inicio**: 29 de abril, 2025
- **Fin**: 30 de junio, 2025

## Objetivos de Área DevOps - 90%

### 1. Fortalecer la seguridad de las aplicaciones y servicios expuestos en la nube - 25%
- **Descripción**: Configurar reglas en Cloud Armor alineadas al OWASP Top 10 antes del 15 de junio, protegiendo al menos el 70% de los servicios HTTP expuestos en producción.
- **Fecha límite**: 15 de junio, 2025
- **Métricas de éxito**:
  - Reglas de Cloud Armor configuradas según OWASP Top 10
  - Al menos 70% de servicios HTTP expuestos protegidos
  - Documentación completa de la implementación
  - Pruebas de efectividad realizadas

### 2. Implementar estrategia de despliegue - 25%
- **Descripción**: Implementar estrategia de despliegue Canary para al menos un nuevo desarrollo y/o al menos 1 proyecto legacy, con rollback automático en caso de errores.
- **Fecha límite**: 30 de junio, 2025
- **Métricas de éxito**:
  - Despliegue Canary implementado en al menos 1 proyecto
  - Mecanismo de rollback automático funcional
  - Documentación del proceso
  - Demostración exitosa del funcionamiento

### 3. Mejorar la calidad del ciclo de desarrollo e integración continua - 25%
- **Descripción**: Implementar Trunk Based Development en los 3 principales repositorios antes del 30 de junio, con flujos documentados y aprobados.
- **Fecha límite**: 30 de junio, 2025
- **Métricas de éxito**:
  - Trunk Based Development implementado en 3 repositorios principales
  - Flujos documentados y aprobados
  - Capacitación a equipos sobre el nuevo flujo
  - Métricas de efectividad establecidas

### 4. Optimización de uso y costos de recursos cloud - 25%
- **Descripción**: Generar un inventario cloud y realizar al menos 1 revisión de este inventario en cada trimestre, documentada con acciones sugeridas.
- **Fecha límite**: 30 de junio, 2025
- **Métricas de éxito**:
  - Inventario cloud completo generado
  - Al menos 1 revisión realizada en Q2
  - Documento con acciones sugeridas
  - Estimación de ahorro potencial

## Objetivos de Impacto Transversal y Colaboración - 10%

### 1. Apoyo técnico a la iniciativa de consolidación de capacidades DevOps - 100%
- **Descripción**: Apoyar la iniciativa de consolidación de capacidades DevOps dentro de los equipos de desarrollo, favoreciendo su autonomía operativa en la gestión de sus entornos y procesos de despliegue en producción.
- **KPI**: Brindar apoyo y soporte técnico a Mica en al menos 3 sesiones de trabajo con el equipo designado.
- **Fecha límite**: 30 de junio, 2025
- **Métricas de éxito**:
  - Al menos 3 sesiones de apoyo a Mica completadas
  - Feedback documentado de cada sesión
  - Mejora en autonomía de equipos de desarrollo
  - Reducción de dependencia en DevOps para tareas rutinarias

## Plan de seguimiento

### Reuniones de seguimiento
- Reuniones 1:1 regulares para revisar progreso
- Revisión quincenal de objetivos de área
- Coordinación con Mica para sesiones de apoyo
- Revisión mensual de inventario cloud

### Documentación de progreso
- Registro de implementaciones de seguridad
- Documentación de estrategia Canary
- Seguimiento de implementación de Trunk Based Development
- Inventario cloud y revisiones

## Conexión con necesidades identificadas

Estos objetivos se alinean directamente con necesidades identificadas en reuniones anteriores:

1. **Distribución de conocimiento DevOps**: El apoyo a la iniciativa de Mica aborda la sobrecarga reportada por Javier (reunión del 27/03/2025) al distribuir conocimiento y responsabilidades.

2. **Enfoque en seguridad y networking**: Los objetivos de Cloud Armor se alinean con su preferencia por seguridad y networking sobre monitoreo (mencionado en memoria).

3. **Mejora de procesos**: La implementación de Trunk Based Development y despliegue Canary mejora los procesos de CI/CD que Javier estaba trabajando (mencionado el 27/03/2025).

4. **Gestión de recursos**: El inventario cloud proporciona estructura para la gestión de recursos, ayudando a priorizar y organizar el trabajo.

## Notas adicionales

Estos objetivos han sido diseñados considerando:

- La situación de Javier como único DevOps en el equipo tras la salida de su colega
- Los signos de sobrecarga y desánimo observados en la reunión del 27/03/2025
- Su próximo viaje de vacaciones a fin de abril con amigos (mencionado el 10/04/2025)
- Su preferencia por seguridad y networking, y su experiencia con Cloud Run y GitHub Actions
- La necesidad de distribuir conocimiento DevOps para reducir dependencias

Los objetivos buscan equilibrar mejoras técnicas significativas con una distribución sostenible de responsabilidades, mientras se alinean con sus intereses y fortalezas. El apoyo a la iniciativa de Mica es particularmente estratégico, ya que ayuda a abordar la sobrecarga a mediano plazo.
