# Plan de Carrera Q2 2025 - Especializaciones GCP (Versión Corregida)

## Resumen de Cambios Implementados

### Filosofía de Especialización Actualizada
- **Base común**: GCP Associate Cloud Engineer obligatorio para ser Senior
- **Especialización solo IC**: Solo Staff Engineers (IC Track) requieren especialización
- **Tech Leads sin especialización**: Se enfocan en liderazgo de equipo, no en expertise técnica profunda
- **Certificación Professional opcional**: Para promoción, obligatoria en primer trimestre como Staff

### Especializaciones Disponibles (Solo Staff Engineer - IC Track)

#### 1. SECURITY SPECIALIST
- **Certificación**: Google Cloud Professional Cloud Security Engineer
- **Enfoque**: Seguridad cloud-native, compliance, DevSecOps organizacional
- **Ideal para**: Desarrolladores interesados en cybersecurity, compliance, y arquitecturas seguras

#### 2. ARCHITECTURE SPECIALIST
- **Certificación**: Google Cloud Professional Cloud Architect
- **Enfoque**: Diseño de sistemas, arquitecturas distribuidas, modernización organizacional
- **Ideal para**: Desarrolladores con fuerte pensamiento sistémico y visión holística

#### 3. DEVSECOPS SPECIALIST
- **Certificación**: Google Cloud Professional Cloud DevOps Engineer
- **Enfoque**: CI/CD, observabilidad, SRE, automatización organizacional
- **Ideal para**: Desarrolladores interesados en operaciones, reliability, y automation

#### 4. MLOPS SPECIALIST
- **Certificación**: Google Cloud Professional Machine Learning Engineer
- **Enfoque**: ML en producción, data pipelines, AI/ML lifecycle organizacional
- **Ideal para**: Desarrolladores interesados en AI/ML, data science, y analytics

## Cronograma de Implementación Actualizado

### Progresión por Nivel

#### Junior (1-3)
- **GCP**: Exposición básica a conceptos cloud y GCP
- **Especialización**: Familiarización con diferentes áreas técnicas

#### Semi Senior (4-6)
- **GCP**: Preparación activa para GCP Associate Cloud Engineer
- **Especialización**: Identificación de área de interés para especialización
- **Proyectos**: Exposición a proyectos del área de especialización elegida

#### Senior (7-9)
- **GCP**: 🎯 **OBLIGATORIO** - Certificación GCP Associate Cloud Engineer
- **Especialización**: Preparación para especialización elegida
- **Proyectos**: Liderazgo en proyectos del área de especialización
- **Decisión**: Elección definitiva entre IC Track (con especialización) o Management Track (sin especialización)

#### Staff Engineer (IC Track)
- **Promoción**: Con especialización ya definida, certificación Professional OPCIONAL
- **Primer Trimestre**: 🎯 **OBLIGATORIO** - Obtener certificación Professional correspondiente
- **Aplicación**: Liderazgo organizacional en área de especialización

#### Tech Lead (Management Track)
- **Promoción**: Sin requisito de especialización técnica
- **Enfoque**: Liderazgo de equipo, people management, delivery
- **GCP**: Solo Associate Cloud Engineer (obtenida como Senior)



## Integración con Plan Existente

### Modificaciones a Niveles Existentes

#### Junior (1-3) y Semi Senior (4-6)
- **Sin cambios mayores**: Mantienen enfoque en fundamentos y crecimiento técnico general
- **Exposición gradual**: Introducción progresiva a conceptos cloud y GCP
- **Exploración**: Exposición a diferentes áreas para identificar intereses

#### Senior (7-9)
- **Nuevo requisito obligatorio**: GCP Associate Cloud Engineer
- **Preparación para especialización**: Identificación y preparación inicial para especialización (solo si eligen IC Track)
- **Decisión de carrera**: Elección entre IC Track (con especialización) o Management Track (sin especialización)

#### Staff Engineer (IC Track)
- **Especialización obligatoria**: Una de las cinco especializaciones
- **Aplicación organizacional**: Liderazgo técnico a nivel empresa
- **Certificación Professional**: Obligatoria en primer trimestre

#### Tech Lead (Management Track)
- **Sin especialización**: Enfoque en liderazgo de equipo
- **Solo base GCP**: Associate Cloud Engineer suficiente
- **Enfoque en people skills**: Desarrollo de habilidades de management

## Beneficios Esperados

### Para la Organización
- **Expertise especializada**: Staff Engineers con conocimiento profundo en áreas críticas
- **Liderazgo balanceado**: Tech Leads enfocados en people management, Staff Engineers en technical leadership
- **Estándares elevados**: Mejores prácticas impulsadas por especialistas certificados
- **Flexibilidad de carrera**: Dos tracks claros con requisitos diferenciados

### Para los Desarrolladores
- **Claridad de carrera**: Paths diferenciados según intereses (técnico vs. management)
- **Especialización dirigida**: Desarrollo profundo en área de interés personal
- **Reconocimiento**: Certificaciones reconocidas en la industria
- **Flexibilidad**: Elección de track según fortalezas y preferencias

## Consideraciones de Implementación

### Inversión Requerida

#### Certificaciones GCP
- **Associate Cloud Engineer**: $125 USD por persona
- **Professional Certifications**: $200 USD por persona



#### Tiempo de Estudio
- **Associate**: 2-3 horas semanales por 2-3 meses
- **Professional**: 3-4 horas semanales por 3-4 meses


### Riesgos y Mitigaciones



#### Riesgo: Diferenciación IC vs Management
- **Mitigación**: Comunicación clara de diferencias, mentoring específico por track

#### Riesgo: Carga de certificaciones
- **Mitigación**: Flexibilidad en timing, apoyo organizacional, tiempo dedicado

### Métricas de Éxito

#### Certificaciones
- **% Senior Engineers con GCP Associate**: Target 100%
- **% Staff Engineers con Professional cert**: Target 100% en primer trimestre


#### Impacto Técnico
- **Mejoras en seguridad**: Reducción de vulnerabilidades
- **Mejoras en arquitectura**: Mejor escalabilidad y mantenibilidad

- **Mejoras en DevOps**: Faster deployment, better observability

## Próximos Pasos

### Inmediatos (Próximas 2 semanas)
1. **Validación con equipo**: Presentar propuesta corregida
2. **Survey de intereses**: Identificar preferencias de track y especialización
3. **Presupuesto**: Calcular inversión total y timeline

### Corto plazo (1-2 meses)
1. **Plan individual**: Cronograma específico por persona
2. **Recursos**: Identificar materiales de estudio y proveedores
3. **Mentoring**: Asignar mentores por especialización

### Mediano plazo (3-6 meses)
1. **Implementación**: Inicio de preparación para certificaciones
2. **Proyectos piloto**: Asignación de proyectos alineados con especializaciones
3. **Seguimiento**: Tracking de progreso y ajustes

## Conclusión

Esta propuesta corregida simplifica la implementación al enfocar las especializaciones solo en el IC Track, manteniendo el Management Track más simple y enfocado en people leadership. La progresión gradual de conocimiento GCP desde Junior hasta la certificación obligatoria en Senior asegura una base sólida, mientras que las especializaciones Professional permiten desarrollo de expertise profunda solo para quienes eligen el path de liderazgo técnico.
