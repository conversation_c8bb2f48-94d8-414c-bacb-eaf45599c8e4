Puesto: Semi Senior Developer

Seniority: Semi Senior

Level: 4, 5 y 6

Responsabilidades:
• Desarrollar features de complejidad media con mínima supervisión
• Resolver bugs de complejidad media a alta
• Escribir pruebas unitarias y de integración
• Realizar code reviews efectivos para compañeros
• Contribuir significativamente a la documentación técnica
• Participar activamente en planificación de sprints
• Proponer mejoras técnicas incrementales
• Identificar y comunicar deuda técnica
• Utiliza métricas para diagnosticar problemas y proponer mejoras. Contribuye a la instrumentación básica del sistema.
• Adaptarse rápidamente a cambios en prioridades, tecnologías o procesos dentro del equipo
• Garantizar que las soluciones implementadas mejoren la experiencia del usuario y no generen fricción innecesaria
• Colaborar con áreas de producto y UX para alinear las decisiones técnicas con las necesidades del usuario final

Funciones Principales:
Innovación
• Identifica oportunidades de mejora en procesos técnicos y de equipo.
• Propone y prueba nuevas herramientas o metodologías para aumentar la eficiencia.
• Cuestiona enfoques tradicionales y sugiere alternativas basadas en datos y buenas prácticas.
Alineación con los OKRs de la empresa
• Optimiza el rendimiento y estabilidad de los sistemas en base a métricas clave.
• Identifica y propone mejoras técnicas que impacten en la eficiencia del equipo.
• Apoya la entrega de features críticas alineadas con los objetivos de negocio.
Ejemplos de Comportamientos:
• Ejemplo positivo: Propone soluciones a problemas técnicos con múltiples alternativas.
• Ejemplo positivo: Proporciona feedback constructivo en code reviews.
• Ejemplo positivo: Se adapta rápidamente cuando un requerimiento cambia y ajusta su enfoque sin afectar la entrega.
• Ejemplo positivo: Identifica puntos de fricción en la experiencia del usuario y propone soluciones técnicas para mejorarlo.
• Área de desarrollo: Ocasionalmente subestima la complejidad de las tareas.

Skills técnicas mínimas esperadas:
DevSecOps y Entrega Continua
• Git avanzado (estrategias de branching,, manejo de conflictos)
• CI/CD multi-ambiente (desarrollo, staging, producción)
• Creación de imágenes Docker personalizadas
• Configuración inicial de pipelines CI/CD (GitHub Actions, GitLab CI, etc.)
• Setup de monitoreo y alertas básicasPrácticas básicas de seguridad en código y gestión de secretos

Diseño y Arquitectura de Sistemas
• Diseño de sistemas pequeños/modulares
• Creación y documentación de APIs escalables para IAIntroducción a conceptos de sistemas distribuidos y arquitecturas cloud

Seguridad, Gobernanza y Gestión de Datos
• Implementación de protocolos de autenticación segura (OAuth2, OIDC)
• Diseño seguro de APIs y servicios
• Estrategias básicas de gobernanza de datos (auditoría, control de accesos)
• Introducción a compliance y normativas (SOC2, GDPR)
• Comprensión inicial de fairness y sesgos en IA

Innovación y Estrategia Técnica
• Propuesta de optimización de sistemas y procesos existentes
• Investigación y evaluación de nuevas tecnologías para el equipo
• Implementación de pequeñas POCs (Proof of Concept)Análisis inicial de impacto técnico de nuevos cambios

Formación académica adicional:
Experiencia comprobable en proyectos reales y autonomía técnica.
Carrera en curso avanzada o finalizada (técnica o universitaria) 

Requisitos de experiencia: Exp tipica: 2 a 4 años

Soft Skills requeridas:
Colaboración & Comunicación
Comprensión de las necesidades del cliente
Adaptabilidad & Agilidad
Capacidad Analítica - Data-Centric Thinking
Empatía
Innovación
Pensamiento Crítico
Autonomía e Impacto

Comportamientos esperados por cada soft skills de acuerdo al Seniority:
1. Comunicación y Colaboración
- Facilita el trabajo en equipo.
- Comunica con claridad y asertividad.
- Escucha activamente.
 - Intercambia ideas con el equipo y explica decisiones técnicas.

2. Autonomía e Impacto
- Gestiona su trabajo con autonomía.
- Prioriza en función del impacto.
- Asume responsabilidad por sus resultados.
- Maneja características completas con mínima supervisión.

3. Adaptabilidad y Agilidad
- Se adapta con autonomía a cambios de contexto.
- Propone soluciones ante imprevistos.
- Apoya a otros en su adaptación.
- Se ajusta a cambios de prioridades y tecnologías con facilidad.

4. Capacidad analítica - Data Centric
- Analiza datos con criterio.
- Relaciona datos con objetivos de negocio.
- Apoya decisiones basadas en evidencia.
- Identifica patrones en datos para resolver problemas complejos.

5. Comprensión de la necesidad del Cliente/Usuario
- Interpreta necesidades implícitas. 
- Propone mejoras alineadas al cliente/usuario. 
- Colabora en soluciones a medida.
- Interpreta requerimientos y sugiere mejoras prácticas.

6. Empatía
- Reconoce distintos puntos de vista.
- Ajusta su estilo según la persona y el contexto.
- Fomenta relaciones positivas.
- Considera necesidades de otros equipos al desarrollar soluciones.

7. Innovación
- Desarrolla nuevas formas de hacer.
- Lidera mejoras en procesos.
- Motiva a otros a pensar diferente.
- Propone mejoras incrementales a sistemas existentes.

8. Pensamiento Crítico
- Evalúa opciones con criterio.
- Detecta riesgos y sugiere mejoras.
- Argumenta con datos.
- Evalúa soluciones alternativas basadas en criterios múltiples.

Proximo Nivel: Senior Developer



