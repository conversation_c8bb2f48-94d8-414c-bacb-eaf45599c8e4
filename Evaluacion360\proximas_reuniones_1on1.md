# Programación de Próximas Reuniones 1:1

## Cronograma Propuesto (Ciclo Quincenal Equilibrado)

### Próximas Reuniones 1:1

| <PERSON><PERSON>bro del Equipo | Última 1:1 | Próxima 1:1 | Horario | Duración | Tipo |
|--------------------|------------|-------------|---------|----------|------|
| <PERSON><PERSON><PERSON> | 07/05/2025 | 21/05/2025 | 10:00 - 10:45 | 45 min | Regular + Revisión Técnica |
| <PERSON><PERSON><PERSON> | 07/05/2025 | 21/05/2025 | 11:30 - 12:15 | 45 min | Regular + Revisión Técnica |
| <PERSON> | 08/05/2025 | 22/05/2025 | 10:30 - 11:30 | 60 min | Regular + Revisión Arquitectura |
| <PERSON><PERSON><PERSON> | 08/05/2025 | 22/05/2025 | 14:00 - 14:45 | 45 min | Regular + Revisión CSSLP |
| <PERSON> | 09/05/2025 | 23/05/2025 | 10:00 - 10:45 | 45 min | Regular + Revisión Técnica |
| <PERSON> | 09/05/2025 | 23/05/2025 | 11:30 - 12:15 | 45 min | Regular + Revisión Objetivos Q2 |

### Ciclo Siguiente

| Miembro del Equipo | Próxima 1:1 | Siguiente 1:1 | Horario | Duración | Tipo |
|--------------------|-------------|---------------|---------|----------|------|
| Cristian Murua | 21/05/2025 | 04/06/2025 | 10:00 - 10:45 | 45 min | Regular + Seguimiento |
| Micaela Percovich | 21/05/2025 | 04/06/2025 | 11:30 - 12:15 | 45 min | Regular + Seguimiento |
| Tobias Piraino | 22/05/2025 | 05/06/2025 | 10:30 - 11:30 | 60 min | Regular + Seguimiento |
| Nicolás Díaz | 22/05/2025 | 05/06/2025 | 14:00 - 14:45 | 45 min | Regular + Seguimiento |
| Joel Camatta | 23/05/2025 | 06/06/2025 | 10:00 - 10:45 | 45 min | Regular + Seguimiento |
| Javier Sankowicz | 23/05/2025 | 06/06/2025 | 11:30 - 12:15 | 45 min | Regular + Seguimiento |

### Distribución Semanal Optimizada

**Semana 1 (21-23 Mayo)**: 2 reuniones por día, 3 días a la semana
- **Miércoles**: Cristian (mañana), Micaela (mañana)
- **Jueves**: Tobias (mañana), Nicolás (tarde)
- **Viernes**: Joel (mañana), Javier (mañana)

**Semana 3 (4-6 Junio)**: Mismo patrón, 2 semanas después
- **Miércoles**: Cristian (mañana), Micaela (mañana)
- **Jueves**: Tobias (mañana), Nicolás (tarde)
- **Viernes**: Joel (mañana), Javier (mañana)

## Checklist General para Todas las Reuniones

### 1. Preparación (antes de la reunión)
- [ ] Revisar notas de la reunión anterior y acciones acordadas
- [ ] Verificar progreso en objetivos Q2 en el dashboard
- [ ] Revisar al menos un ejemplo de trabajo técnico reciente (código, diseño, etc.)
- [ ] Preparar feedback específico basado en trabajo técnico revisado
- [ ] Identificar temas específicos para discusión técnica profunda
- [ ] Revisar plan de carrera y próximos pasos de desarrollo

### 2. Estructura de la Reunión (nuevo formato balanceado)

#### Apertura (5 min)
- [ ] Verificar bienestar personal y profesional
- [ ] Establecer agenda y expectativas para la reunión

#### Seguimiento de Acciones Previas (5 min)
- [ ] Revisar estado de acciones acordadas en la reunión anterior
- [ ] Documentar resultados concretos de cada acción
- [ ] Identificar bloqueos o desafíos encontrados

#### Progreso en Objetivos Q2 (10 min)
- [ ] Revisar métricas específicas de progreso para cada objetivo
- [ ] Discutir bloqueos o desafíos para el avance
- [ ] Ajustar próximos pasos o prioridades si es necesario
- [ ] Conectar objetivos con impacto en el proyecto/organización

#### Revisión Técnica (15 min)
- [ ] Discutir trabajo técnico específico (preparado previamente)
- [ ] Proporcionar feedback concreto sobre fortalezas técnicas observadas
- [ ] Identificar áreas específicas de mejora técnica
- [ ] Discutir decisiones técnicas recientes y alternativas consideradas

#### Desarrollo Profesional (10 min)
- [ ] Revisar progreso en plan de carrera
- [ ] Discutir oportunidades de crecimiento identificadas
- [ ] Conectar actividades actuales con objetivos de desarrollo
- [ ] Identificar recursos o apoyo necesarios

#### Cierre y Próximos Pasos (5 min)
- [ ] Resumir puntos clave discutidos
- [ ] Acordar acciones específicas con responsables y fechas
- [ ] Establecer expectativas para la próxima reunión
- [ ] Solicitar feedback sobre la reunión y el nuevo formato

### 3. Documentación (después de la reunión)
- [ ] Documentar puntos clave y decisiones (formato conciso)
- [ ] Registrar acciones acordadas en sistema de seguimiento
- [ ] Actualizar dashboard de objetivos Q2 con progreso
- [ ] Programar seguimientos necesarios antes de la próxima reunión 1:1

## Checklists Específicos por Miembro

### Cristian Murua
- [ ] Verificar finalización del curso FHIR (fecha límite: 15 de mayo)
- [ ] Revisar lista de logros profesionales (accionable previo)
- [ ] Discutir progreso en plan intensivo de 6 semanas con Tobias (Semana 1: Fundamentos)
- [ ] Revisar accionables completados (CRUD REST tradicional, investigación TCP en NestJS)
- [ ] Evaluar comprensión de conceptos fundamentales (APIs, protocolos, REST vs RPC vs GraphQL)
- [ ] Discutir uso de IA como herramienta de aprendizaje
- [ ] Planificar seguimiento para Semana 2 (Fundamentos de NestJS)
- [ ] Evaluar estado anímico y confianza profesional

### Micaela Percovich
- [ ] Verificar bienestar familiar (seguimiento operación del padre)
- [ ] Revisar lista de logros profesionales (accionable previo)
- [ ] Discutir progreso en capacitaciones DevSecOp interequipos
- [ ] Verificar avance en capacitación track DevOps/GCP
- [ ] Revisar balance entre estudios universitarios y responsabilidades laborales
- [ ] Discutir colaboración con Javier en distribución de conocimiento DevOps

### Tobias Piraino
- [ ] Revisar lista de logros profesionales (accionable previo)
- [ ] Discutir avances en revisión de capa AI, casos de uso y entregables
- [ ] Verificar progreso en capacitación System Design
- [ ] Revisar implementación del plan intensivo de 6 semanas para Cristian
- [ ] Discutir resultados de la Semana 1 y preparación para Semana 2
- [ ] Evaluar efectividad de la mentoría y ajustes necesarios
- [ ] Discutir detalles de propuesta arquitectural para multitenancy
- [ ] Evaluar progreso hacia nivel Senior (alineado con plan de carrera)

### Nicolás Díaz
- [ ] Verificar revisión del primer módulo del plan de capacitación SDLC/AppSec (fecha: 16 de mayo)
- [ ] Revisar lista de logros profesionales (accionable previo)
- [ ] Discutir implementación de proceso de prácticas de seguridad en SDLC
- [ ] Verificar estado de recuperación de lesión
- [ ] Compartir actualización sobre estrategia para autonomía de backend B2B (conversación con CTO)
- [ ] Discutir balance entre apoyo a B2B y objetivos de especialización en seguridad

### Joel Camatta
- [ ] Revisar lista de logros profesionales (accionable previo)
- [ ] Discutir experiencia detallada con capacitación System Design
- [ ] Solicitar ejemplos específicos de aplicación de conceptos aprendidos
- [ ] Verificar progreso en objetivos de proyecto
- [ ] Discutir posible colaboración con Tobias en temas de arquitectura
- [ ] Evaluar progreso hacia nivel Senior (alineado con plan de carrera)

### Javier Sankowicz
- [ ] Revisar lista de logros profesionales (accionable previo)
- [ ] Compartir información sobre estado de aporte al proyecto MSI
- [ ] Confirmar detalles sobre transición del cliente de Perú (reducción inmediata, salida 1 de junio)
- [ ] Discutir progreso en objetivos Q2 (Cloud Armor, Canary, Trunk Based Development, optimización)
- [ ] Planificar cobertura durante vacaciones de junio
- [ ] Revisar colaboración con Micaela en distribución de conocimiento DevOps

## Notas para Implementación

1. **Ciclo quincenal equilibrado**: Las reuniones se han programado en un ciclo quincenal consistente para todos los miembros, con exactamente 14 días entre cada reunión 1:1, manteniendo el mismo día de la semana para cada persona.

2. **Distribución optimizada**: Se han distribuido las reuniones en 3 días por semana (miércoles, jueves, viernes), con solo 2 reuniones por día, permitiendo tiempo adecuado para preparación y seguimiento.

3. **Duración extendida**: Las reuniones se han programado con mayor duración (45-60 min vs. 15-40 min anteriores) para permitir mayor profundidad técnica y seguimiento estructurado.

4. **Preparación técnica**: Dedica tiempo específico antes de cada reunión para revisar trabajo técnico que servirá como base para feedback específico. Con solo 2 reuniones por día, tendrás tiempo suficiente para esta preparación.

5. **Documentación eficiente**: Utiliza el nuevo formato para documentar de manera más concisa pero efectiva, enfocándote en acciones y resultados.

6. **Seguimiento entre reuniones**: Implementa check-ins breves entre ciclos de reuniones 1:1 para verificar progreso en acciones críticas.

7. **Feedback bidireccional**: Solicita activamente feedback sobre el nuevo formato de reunión para iterarlo y mejorarlo.

Esta estructura implementa varias de las recomendaciones del plan de acción, particularmente:
- Reestructurar formato de reuniones 1:1
- Aumentar profundidad técnica de las discusiones
- Mejorar seguimiento de objetivos Q2
- Implementar feedback técnico más específico
- Balancear aspectos administrativos y técnicos
