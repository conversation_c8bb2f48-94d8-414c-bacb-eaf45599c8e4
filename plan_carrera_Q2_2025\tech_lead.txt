Puesto: Tech lead

Seniority: Management

Level: 

Responsabilidades:
• Liderazgo técnico de un equipo pequeño (3-5 personas)
• Contribución directa al código (40-60% del tiempo)
• Mentoring técnico continuo al equipo
• Planificación técnica de sprints y roadmap técnico
• Facilitación de decisiones técnicas y arquitectónicas
• Colaboración estrecha con Product Managers
• Propiedad del sistema y su calidad técnica
• Gestión de deuda técnica
• Facilitación de retrospectivas técnicas
• Representación técnica del equipo ante otros grupos
• Resolución de bloqueos técnicos
• Usa métricas para priorizar tareas y medir la efectividad del equipo. Promueve la toma de decisiones basada en datos.
• Adaptar la estrategia técnica del equipo en función de cambios en el negocio o la tecnología sin perder estabilidad ni eficiencia
• Garantizar que las soluciones técnicas sean escalables y mejoren la experiencia del usuario final
• Alinear el roadmap técnico con las necesidades del usuario y los objetivos de la empresa, asegurando que las soluciones entregadas sean realmente útiles

Funciones Principales:
• Comprensión profunda del stack técnico (Backend o Frontend)
• Capacidad de code review de alto nivel y orientación arquitectónica
• Entendimiento holístico de la arquitectura del sistema
• Capacidad para desbloquear obstáculos técnicos complejos
• Conocimiento de patrones de diseño y mejores prácticas
• Habilidad para balancear calidad técnica y entrega de valor
• Comprensión de trade-offs técnicos y sus implicaciones
• Conocimiento de operaciones y monitoreo
• Capacidad para adaptar estrategias técnicas según la evolución del producto y las necesidades de los usuarios

Innovación
• Fomenta la experimentación en el equipo asegurando un balance entre innovación y estabilidad.
• Facilita la adopción de nuevas tecnologías cuando agregan valor al negocio.
• Mejora la cultura técnica promoviendo prácticas avanzadas de desarrollo.
Alineación con los OKRs de la empresa
• Garantiza la entrega de proyectos técnicos alineados con las prioridades estratégicas.
• Asegura que el equipo trabaje en iniciativas de alto impacto para la empresa.
• Facilita la colaboración entre Ingeniería y Producto para optimizar la planificación técnica.
Ejemplos de Comportamientos:
• Ejemplo positivo: Desbloquea al equipo identificando soluciones técnicas a problemas complejos.
• Ejemplo positivo: Balancea efectivamente la calidad del código con la necesidad de entregar.
• Ejemplo positivo: Adapta la planificación técnica a cambios estratégicos de la empresa sin afectar el ritmo del equipo.
• Ejemplo positivo: Prioriza soluciones que no solo sean técnicamente correctas, sino que mejoren la experiencia del usuario.
• Área de desarrollo: Puede tener dificultad delegando trabajo técnico interesante.

Skills técnicas mínimas esperadas:
Al menos las mismas de un nivel Senior

Formación académica adicional:

- Título universitario en Ingeniería o afines (deseable)- Experiencia sólida como Senior Engineer
- Formación complementaria en liderazgo, metodologías ágiles, arquitectura
- Certificaciones técnicas avanzadas (Cloud, DevOps, etc.) 

Requisitos de experiencia: Experiencia típica: 5+ años (incluyendo exp. técnica)

Soft Skills requeridas:
Colaboración & Comunicación
Comprensión de las necesidades del cliente
Adaptabilidad & Agilidad
Capacidad Analítica - Data-Centric Thinking
Empatía
Innovación
Pensamiento Crítico
Autonomía e Impacto
Liderazgo & Mentoring
Resolución de Conflictos

Comportamientos esperados por cada soft skills de acuerdo al Seniority:
1. Comunicación y Colaboración
• Coordina entre producto, diseño y desarrollo.
• Establece expectativas claras y da feedback continuo.
• Escucha activamente a su equipo y stakeholders.
• Resuelve conflictos o malentendidos de manera constructiva.
2. Autonomía e Impacto
• Toma decisiones estratégicas de delivery técnico.
• Balancea deuda técnica con velocidad de entrega.
• Asume accountability por la performance del equipo.
• Ayuda a priorizar en base a impacto técnico y de negocio.
3. Adaptabilidad y Agilidad
• Fomenta ciclos de iteración cortos y mejoras continuas.
• Se adapta a cambios en la hoja de ruta de producto sin perder foco.
• Introduce y ajusta procesos según contexto (ágil sin dogma).
4. Capacidad Analítica – Data Centric
• Usa métricas de equipo (velocidad, calidad, bugs) para detectar mejoras.
• Traduce datos de negocio en decisiones técnicas.
• Promueve el uso de datos en estimaciones y mejoras técnicas.
5. Comprensión del Cliente/Usuario
• Facilita el entendimiento de necesidades de usuario dentro del equipo.
• Prioriza features con mayor impacto desde lo técnico y lo humano.
• Equilibra necesidades técnicas con tiempos de mercado.
6. Empatía
• Conecta con las necesidades individuales del equipo.
• Gestiona emociones y expectativas de los miembros.
• Fomenta un entorno de confianza y crecimiento.
7. Innovación
• Da espacio para experimentar e innovar dentro del equipo.
• Aplica innovaciones que mejoran velocidad, calidad o experiencia del equipo.
• Promueve retrospectivas y mejoras autogestionadas.
8. Pensamiento Crítico
• Filtra información técnica, de producto y negocio para tomar decisiones alineadas.
• Se anticipa a riesgos técnicos o humanos.
• Toma decisiones difíciles con racionalidad y empatía.
9. Liderazgo y Mentoring
• Facilita el desarrollo profesional del equipo.
• Delegación efectiva y empoderamiento del equipo.
• Brinda feedback constructivo y guía de crecimiento.
• Actúa como ejemplo de responsabilidad, foco y resiliencia.

10. Resolución de Conflictos
• Identifica y aborda conflictos interpersonales dentro del equipo antes que escalen.
• Facilita conversaciones difíciles entre miembros del equipo o con otros stakeholders.
• Balancea diferentes perspectivas y necesidades para llegar a soluciones consensuadas.
• Transforma conflictos en oportunidades de crecimiento para el equipo.
• Establece procesos claros para la toma de decisiones que previenen conflictos recurrentes.

Proximo Nivel: -



