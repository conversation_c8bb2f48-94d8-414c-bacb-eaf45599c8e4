# Inicio de Capacitación: System Design y Arquitectura - 24/04/2025

## Información básica
- **Miembro del equipo**: <PERSON>
- **<PERSON><PERSON>**: 24/04/2025
- **Tipo de actividad**: Inicio de capacitación
- **<PERSON><PERSON> clave**: System Design, Arquitectura, Desarrollo profesional
- **Relación con plan de carrera**: Desarrollo de habilidades en diseño y arquitectura de sistemas para progresión a Senior

## Detalles de la capacitación

Se ha iniciado el programa de capacitación en "System Design y Arquitectura de Sistemas" para <PERSON> y <PERSON>. Este programa se alinea con el plan de desarrollo profesional de Joel, específicamente con su interés en el track de Individual Contributor con enfoque en diseño y arquitectura de sistemas, y su progresión planificada a Senior en Q2 2025.

### Estructura del programa

- **Duración**: 24 semanas (aproximadamente 6 meses)
- **Formato**: Entrega de material una vez por semana
- **Metodología**: Sesiones teórico-prácticas con accionables concretos
- **Evaluación**: Accionables semanales, seguimiento mensual, revisiones por fase, proyecto final integrador

### Contenido general

El programa está estructurado en tres fases:

1. **Fundamentos de System Design** (Semanas 1-8)
   - Conceptos básicos, características no funcionales, sistemas distribuidos, dimensionamiento, DNS, load balancers, caching, bases de datos

2. **Componentes y Patrones de Arquitectura** (Semanas 9-16)
   - Microservicios, mensajería, patrones de diseño, APIs, autenticación, testing, monitoreo, seguridad

3. **Implementación de Casos Prácticos** (Semanas 17-24)
   - Arquitecturas serverless, Kubernetes, escalabilidad, circuit breaking, alta disponibilidad, big data, SRE

### Conexión con intereses previos

Esta capacitación se conecta directamente con varios aspectos del perfil profesional de Joel:

- Su interés en el track de Individual Contributor (expresado en la reunión del 21/03)
- Su enfoque en diseño y arquitectura de sistemas (reunión del 21/03)
- Su experiencia previa como Cloud Engineer, que proporciona una base sólida para varios temas del programa
- Su necesidad de "pulir" temas técnicos y desarrollar mayor seguridad (identificada en la reunión del 21/03)

## Próximos pasos

- Seguimiento de la primera sesión y accionable asignado
- Coordinación con Tobias para posibles sesiones de discusión conjunta
- Integración de los temas de la capacitación con proyectos actuales cuando sea posible

## Notas adicionales

El plan completo de capacitación se encuentra documentado en `1on1_meetings/capacitacion/plan_system_design_arquitectura.md` para referencia y seguimiento.

Esta capacitación representa una oportunidad significativa para el desarrollo profesional de Joel, proporcionando una estructura formal para su crecimiento en áreas de interés y apoyando su progresión a Senior. También ofrece un espacio para la colaboración con Tobias, lo que puede enriquecer la experiencia de aprendizaje para ambos.
