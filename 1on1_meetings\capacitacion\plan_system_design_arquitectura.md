# Plan de Capacitación en System Design y Arquitectura de Sistemas

## Información General

- **Inicio**: 24 de abril de 2025
- **Frecuencia**: Entrega de material una vez por semana
- **Participantes**: <PERSON> y <PERSON>
- **Formato**: Sesiones teórico-prácticas con accionables concretos
- **Duración total**: 24 semanas (aproximadamente 6 meses)

## Público Objetivo y Stack Tecnológico

- **Público**: Equipo de desarrolladores backend
- **Stack**: Agnóstico, con enfoque en:
  - TypeScript
  - Nest.js
  - NextJS
  - PostgreSQL
  - MongoDB
  - GCP

## Estructura de las Sesiones

Cada sesión sigue la siguiente estructura:
- Discusión y preguntas de conceptos teóricos (20 minutos)
- Asignación de accionable (5-10 minutos)
- Recursos bibliográficos recomendados:
  - Referencias principales: Libros fundamentales para profundizar
  - Referencias complementarias: Artículos y documentación en línea
  - Referencias adicionales: Videos, papers y tutoriales prácticos

## Evaluación y Seguimiento

- Cada semana incluye accionables específicos que serán evaluados
- Se monitorizará mensualmente el progreso
- Al final de cada fase, se realizará una revisión integral de conceptos
- El caso práctico integrador de la última semana funciona como proyecto final

## Contenido Detallado

### Fase 1: Fundamentos de System Design (Semanas 1-8)

#### Tema 1: Introducción al System Design
- **Sesión 1**: Conceptos Fundamentales
- **Sesión 2**: Preparación para Entrevistas de System Design

#### Tema 2: Características No Funcionales
- **Sesión 1**: Disponibilidad y Confiabilidad
- **Sesión 2**: Escalabilidad y Mantenibilidad

#### Tema 3: Abstracciones en Sistemas Distribuidos
- **Sesión 1**: Modelos de Consistencia
- **Sesión 2**: Modelos de Fallo y Comunicación

#### Tema 4: Cálculos de Dimensionamiento
- **Sesión 1**: Estimaciones Back-of-the-Envelope
- **Sesión 2**: Estimación de Recursos

#### Tema 5: DNS y Load Balancers
- **Sesión 1**: Sistema de Nombres de Dominio (DNS)
- **Sesión 2**: Balanceadores de Carga (Reverse and Forward Proxies, LB Layer 4 vs Layer 7)

#### Tema 6: Caching
- **Sesión 1**: Fundamentos de Caching
- **Sesión 2**: CDN y Caching Distribuido (estrategias de Caché)

#### Tema 7: Bases de Datos - Parte 1
- **Sesión 1**: Tipos de Bases de Datos
- **Sesión 2**: Replicación de Datos

#### Tema 8: Bases de Datos - Parte 2
- **Sesión 1**: Particionamiento de Datos
- **Sesión 2**: Optimización de Consultas

### Fase 2: Componentes y Patrones de Arquitectura (Semanas 9-16)

#### Tema 9: Microservicios
- **Sesión 1**: Arquitectura de Microservicios
- **Sesión 2**: Comunicación entre Microservicios

#### Tema 10: Mensajería y Comunicación Asíncrona
- **Sesión 1**: Colas de Mensajes
- **Sesión 2**: Kafka y Streaming

#### Tema 11: Patrones de Diseño para Sistemas Distribuidos
- **Sesión 1**: Patrones de Resiliencia
- **Sesión 2**: Patrones de Consistencia

#### Tema 12: API Design y Management
- **Sesión 1**: Principios de Diseño de APIs
- **Sesión 2**: API Gateways y Management

#### Tema 13: Auth en Sistemas Distribuidos
- **Sesión 1**: Autenticación y Autorización
- **Sesión 2**: Gestión de Identidad en Microservicios

#### Tema 14: Testing en Arquitecturas Distribuidas
- **Sesión 1**: Estrategias de Testing
- **Sesión 2**: Pruebas de Resiliencia y Chaos Engineering

#### Tema 15: Monitoreo y Observabilidad
- **Sesión 1**: Métricas y Alertas
- **Sesión 2**: Logging y Tracing

#### Tema 16: Seguridad en Arquitecturas Distribuidas
- **Sesión 1**: Principios de Seguridad
- **Sesión 2**: Seguridad en CI/CD

### Fase 3: Implementación de Casos Prácticos (Semanas 17-24)

#### Tema 17: Arquitecturas Serverless
- **Sesión 1**: Fundamentos Serverless
- **Sesión 2**: API Gateway y Servicios Gestionados

#### Tema 18: Container Orchestration
- **Sesión 1**: Kubernetes Fundamentals
- **Sesión 2**: Operaciones en Kubernetes

#### Tema 19: Patrones de Escalabilidad
- **Sesión 1**: Estrategias de Escalado Horizontal
- **Sesión 2**: Backend for Frontend y API Composition

#### Tema 20: Circuit Breaking y Throttling
- **Sesión 1**: Circuit Breakers y Bulkheads
- **Sesión 2**: Rate Limiting y Throttling

#### Tema 21: Diseño de Sistemas de Alta Disponibilidad
- **Sesión 1**: Arquitecturas Multi-Región
- **Sesión 2**: Zero-Downtime Deployments

#### Tema 22: Manejo de Big Data
- **Sesión 1**: Procesamiento de Datos a Gran Escala
- **Sesión 2**: Streaming y Procesamiento en Tiempo Real

#### Tema 23: Site Reliability Engineering (SRE)
- **Sesión 1**: Principios de SRE
- **Sesión 2**: Incident Management y Postmortems

#### Tema 24: Revisión Final y Caso Práctico Integrador
- **Sesión 1**: Revisión Integral de Conceptos
- **Sesión 2**: Caso Práctico Integrador

## Registro de Sesiones

| Fecha | Tema | Participantes | Accionable | Observaciones |
|-------|------|--------------|------------|---------------|
| 24/04/2025 | Introducción al System Design - Conceptos Fundamentales | Tobias, Joel | *Pendiente* | Inicio del programa |
| | | | | |

## Notas y Ajustes

*Esta sección se actualizará con notas sobre el progreso, ajustes al plan y observaciones relevantes durante la implementación del programa.*
