Puesto: Tech Lead (Management Track) - Con Especializaciones

Seniority: Tech Lead

Level: 5

CERTIFICACIÓN BASE OBLIGATORIA:
• Google Cloud Associate Cloud Engineer (prerequisito para todas las especializaciones)

ESPECIALIZACIONES DISPONIBLES:
Al llegar a Tech Lead, se debe elegir UNA especialización y obtener la certificación Professional correspondiente:

1. TECH LEAD - SECURITY SPECIALIST
   Certificación: Google Cloud Professional Cloud Security Engineer
   
2. TECH LEAD - ARCHITECTURE SPECIALIST  
   Certificación: Google Cloud Professional Cloud Architect
   
3. TECH LEAD - DEVSECOPS SPECIALIST
   Certificación: Google Cloud Professional Cloud DevOps Engineer
   
4. TECH LEAD - MLOPS SPECIALIST
   Certificación: Google Cloud Professional Machine Learning Engineer
   
5. TECH LEAD - QUALITY SPECIALIST
   Certificación: Google Cloud Professional Cloud Developer + Certificaciones QA externas

=== RESPONSABILIDADES COMUNES A TODAS LAS ESPECIALIZACIONES ===

Responsabilidades Generales:
• Liderazgo técnico de un equipo pequeño (3-5 personas)
• Contribución directa al código (40-60% del tiempo)
• Mentoring técnico continuo al equipo
• Planificación técnica de sprints y roadmap técnico
• Facilitación de decisiones técnicas y arquitectónicas
• Colaboración estrecha con Product Managers
• Propiedad del sistema y su calidad técnica
• Gestión de deuda técnica
• Facilitación de retrospectivas técnicas
• Representación técnica del equipo ante otros grupos
• Resolución de bloqueos técnicos
• Usar métricas para priorizar tareas y medir la efectividad del equipo
• Adaptar la estrategia técnica del equipo en función de cambios en el negocio
• Garantizar que las soluciones técnicas sean escalables y mejoren la experiencia del usuario
• LIDERAR EN SU ÁREA DE ESPECIALIZACIÓN dentro del equipo y proyectos asignados

Funciones Principales:
• Comprensión profunda del stack técnico (Backend o Frontend)
• Capacidad de code review de alto nivel y orientación arquitectónica
• Entendimiento holístico de la arquitectura del sistema
• Capacidad para desbloquear obstáculos técnicos complejos
• Conocimiento de patrones de diseño y mejores prácticas
• Habilidad para balancear calidad técnica y entrega de valor
• Comprensión de trade-offs técnicos y sus implicaciones
• Conocimiento de operaciones y monitoreo

=== ESPECIALIZACIONES DETALLADAS ===

1. TECH LEAD - SECURITY SPECIALIST
Certificación Requerida: Google Cloud Professional Cloud Security Engineer

Responsabilidades Específicas del Equipo:
- Liderar implementación de security practices en el equipo
- Asegurar que el código del equipo cumple con estándares de seguridad
- Facilitar security reviews y threat modeling en proyectos del equipo
- Mentorizar al equipo en secure coding practices
- Coordinar con Security team organizacional para implementar políticas
- Liderar respuesta a security incidents que afecten al equipo
- Establecer security testing como parte del pipeline del equipo

Skills Técnicas Adicionales:
- Implementación de security controls en aplicaciones
- Conocimiento de OWASP Top 10 y secure coding
- Experiencia en security testing y code analysis
- Dominio de IAM y access controls en GCP

2. TECH LEAD - ARCHITECTURE SPECIALIST
Certificación Requerida: Google Cloud Professional Cloud Architect

Responsabilidades Específicas del Equipo:
- Liderar decisiones arquitectónicas del equipo y proyectos
- Diseñar arquitectura de nuevas features y sistemas del equipo
- Facilitar architectural reviews y design sessions
- Mentorizar al equipo en design patterns y architectural thinking
- Coordinar con Architecture team para alineación organizacional
- Liderar refactoring y modernization efforts del equipo
- Establecer architectural standards para el equipo

Skills Técnicas Adicionales:
- Diseño de microservicios y distributed systems
- Conocimiento profundo de cloud-native patterns
- Experiencia en system design y scalability
- Dominio de networking y infrastructure patterns

3. TECH LEAD - DEVSECOPS SPECIALIST
Certificación Requerida: Google Cloud Professional Cloud DevOps Engineer

Responsabilidades Específicas del Equipo:
- Liderar implementación y mejora de CI/CD pipelines del equipo
- Establecer monitoring, logging y observability para proyectos del equipo
- Facilitar adoption de DevOps practices y culture
- Mentorizar al equipo en infrastructure as code y automation
- Coordinar con Platform/DevOps team para tooling y standards
- Liderar incident response y post-mortems del equipo
- Establecer SLIs/SLOs para servicios del equipo

Skills Técnicas Adicionales:
- Expertise en CI/CD tools y strategies
- Conocimiento profundo de monitoring y observability
- Experiencia en infrastructure automation
- Dominio de containerization y orchestration

4. TECH LEAD - MLOPS SPECIALIST
Certificación Requerida: Google Cloud Professional Machine Learning Engineer

Responsabilidades Específicas del Equipo:
- Liderar implementación de ML features y capabilities en el equipo
- Establecer ML pipelines y model deployment practices
- Facilitar adoption de ML best practices y experimentation
- Mentorizar al equipo en ML engineering y data science collaboration
- Coordinar con Data Science team para model integration
- Liderar ML model monitoring y performance optimization
- Establecer A/B testing y experimentation framework para el equipo

Skills Técnicas Adicionales:
- Implementación de ML pipelines en producción
- Conocimiento de ML lifecycle y model management
- Experiencia en data engineering y feature engineering
- Dominio de ML serving y real-time inference

5. TECH LEAD - QUALITY SPECIALIST
Certificación Requerida: Google Cloud Professional Cloud Developer + Certificaciones QA

Responsabilidades Específicas del Equipo:
- Liderar implementación de testing strategies y quality practices
- Establecer testing frameworks y automation para el equipo
- Facilitar adoption de quality engineering practices
- Mentorizar al equipo en testing methodologies y quality mindset
- Coordinar con QA team para testing standards y tools
- Liderar quality metrics y continuous improvement initiatives
- Establecer performance testing y quality gates

Skills Técnicas Adicionales:
- Expertise en testing frameworks y automation
- Conocimiento profundo de quality metrics y practices
- Experiencia en performance testing y optimization
- Dominio de test data management y environment setup

=== SKILLS COMUNES A TODAS LAS ESPECIALIZACIONES ===

Formación académica adicional:
- Título universitario en Ingeniería o afines (deseable)
- Experiencia sólida como Senior Engineer
- Formación complementaria en liderazgo, metodologías ágiles, arquitectura
- Certificaciones técnicas avanzadas (Cloud, DevOps, etc.)

Requisitos de experiencia: 5+ años (incluyendo experiencia técnica)

Soft Skills requeridas:
- Comunicación y Colaboración
- Comprensión de las necesidades del cliente
- Adaptabilidad y Agilidad
- Capacidad Analítica - Data-Centric Thinking
- Empatía
- Innovación
- Pensamiento Crítico
- Autonomía e Impacto
- Liderazgo y Mentoring
- Resolución de Conflictos

Próximo Nivel: Engineering Manager / Senior Tech Lead (futuro)

=== CRITERIOS DE PROGRESIÓN ===

Para ser promovido a Tech Lead (cualquier especialización):
1. Completar certificación GCP Associate Cloud Engineer
2. Demostrar expertise en el área de especialización elegida
3. Liderar exitosamente un equipo técnico de 3-5 personas
4. Obtener la certificación Professional correspondiente a la especialización
5. Demostrar capacidad de mentoring y desarrollo de equipo
6. Mostrar impacto medible en la productividad y calidad del equipo
7. Balancear efectivamente contribución técnica (40-60%) con liderazgo

=== DIFERENCIAS CLAVE CON STAFF ENGINEER ===

TECH LEAD (Management Track):
- Enfoque en liderazgo de equipo y people management
- Responsabilidad directa por la productividad del equipo
- 40-60% tiempo en código, resto en liderazgo
- Especialización aplicada a nivel de equipo/proyecto
- Desarrollo de soft skills de management

STAFF ENGINEER (IC Track):
- Enfoque en liderazgo técnico e influencia organizacional
- Responsabilidad por arquitectura y estándares técnicos
- 30-50% tiempo en código, resto en arquitectura/mentoría
- Especialización aplicada a nivel organizacional
- Desarrollo de expertise técnica profunda
