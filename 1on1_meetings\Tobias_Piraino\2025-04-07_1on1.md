# 1:1 con <PERSON> - 07/04/2025

## Información básica
- **Miembro del equipo**: <PERSON>
- **<PERSON><PERSON>**: 07/04/2025
- **Duración**: 45 minutos (11:15 - 12:00)
- **Ciclo de revisión**: Regular
- **<PERSON><PERSON> clave**: Técnico, Arquitectura, Diagramas C4
- **Nivel de satisfacción**: 4 - <PERSON><PERSON> buena
- **Seguimiento requerido**: Sí
- **Relación con OKRs**: Desarrollo de habilidades en arquitectura de software

## Seguimiento de acciones previas
- Seguimiento de la reunión del 25/03/2025 donde se introdujeron los diagramas C4 y ADR

## Temas tratados
- Experiencia de Tobias trabajando con diagramas C4
- Aplicación práctica de los conceptos arquitecturales
- Solicitud de ayuda para generar diagramas C4 usando los 4 niveles
- Ejemplos de diagramas C4 desde la página Structurizr

## Logros desde la última reunión
- Tobias ha comenzado a trabajar con diagramas C4
- Aplicación práctica de los conceptos introducidos en la reunión anterior

## Bloqueos reportados
- Dificultad para implementar los 4 niveles de diagramas C4

## Desafíos actuales
- Dominar la metodología completa de diagramas C4
- Aplicar los 4 niveles de abstracción de manera efectiva

## Bienestar y equilibrio trabajo-vida
*No se menciona específicamente en esta reunión*

## Feedback bidireccional
### Observaciones sobre Tobias
- Muestra interés activo en aplicar los conceptos de arquitectura
- Ha tomado la iniciativa de trabajar con diagramas C4
- Busca profundizar su conocimiento solicitando ayuda específica

### Feedback para el líder
*No se menciona feedback específico*

## Plan de Carrera (Observaciones / acciones)
- Progreso en el desarrollo de habilidades de arquitectura de software
- Aplicación práctica de herramientas arquitecturales (diagramas C4)

## Métricas de crecimiento
*No se establecieron métricas específicas en esta reunión*

## Recursos de aprendizaje recomendados
- Ejemplos de diagramas C4 de la página Structurizr
- Recursos sobre los 4 niveles de diagramas C4 (Contexto, Contenedor, Componente, Código)

## Alineación con valores de la empresa
*No se discutió específicamente en esta reunión*

## Objetivos para la próxima reunión
- Revisar los diagramas C4 creados por Tobias
- Evaluar la aplicación de los 4 niveles
- Identificar áreas de mejora en la documentación arquitectural

## Acuerdos y acciones
| Acción | Responsable | Fecha límite | Prioridad |
|--------|-------------|--------------|-----------|
| Revisar ejemplos de Structurizr proporcionados | Tobias | Próxima reunión | Alta |
| Crear diagramas C4 aplicando los 4 niveles | Tobias | 2 semanas | Alta |
| Compartir recursos adicionales sobre diagramas C4 | Tech Lead | 1 semana | Media |

## Notas adicionales
Esta reunión regular se centró principalmente en aspectos técnicos, específicamente en la aplicación práctica de los diagramas C4 que fueron introducidos en la reunión del 25 de marzo. Tobias compartió su experiencia trabajando con estos diagramas, lo que demuestra que ha tomado la iniciativa de aplicar los conceptos discutidos previamente.

Un punto importante fue su solicitud de ayuda específica para generar diagramas C4 utilizando los 4 niveles de abstracción (Contexto, Contenedor, Componente y Código). Esto indica un interés genuino en dominar esta herramienta arquitectural y aplicarla de manera completa. Como respuesta, se le proporcionaron ejemplos de base desde la página Structurizr, que es una referencia reconocida para esta metodología.

Esta conversación representa un avance concreto en el desarrollo de Tobias hacia roles con mayor enfoque en arquitectura, alineándose con su interés expresado en reuniones anteriores y con el plan de capacitación en System Design y Arquitectura de Sistemas programado para Q2 2025. La aplicación práctica de estos conceptos es un paso importante en su progresión hacia el nivel Senior.
