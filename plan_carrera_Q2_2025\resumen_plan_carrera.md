# Resumen del Plan de Carrera Q2 2025

## Estructura Jerárquica

```
CTO
├── Tech Lead (Track Management)
└── Staff Engineer (Track Contribuidor Individual)
    └── Senior Developer
        └── Semi Senior Developer
            └── Junior Developer
```

## Nivel<PERSON> y Seniority

| Puesto | Seniority | Level | Experiencia Típica |
|--------|-----------|-------|-------------------|
| Junior Developer | Junior | 1, 2, 3 | 0-2 años |
| Semi Senior Developer | Semi Senior | 4, 5, 6 | 2-4 años |
| Senior Developer | Senior | 7, 8, 9 | 4-6 años |
| Staff Engineer | Senior/Staff/Principal | - | +5 años |
| Tech Lead | Management | - | +5 años (incluyendo exp. técnica) |

## Tracks de Carrera

### Track de Contribuidor Individual (IC)
Junior → Semi Senior → Senior → Staff Engineer

### Track de Management
Junior → Semi Senior → Senior → Tech Lead

## Áreas de Habilidades Técnicas

1. **DevOps y Entrega Continua**
   - Git, CI/CD, Docker, Kubernetes, monitoreo, testing

2. **Diseño y Arquitectura de Sistemas**
   - Patrones de diseño, arquitectura, APIs, sistemas distribuidos

3. **Seguridad, Gobernanza y Gestión de Datos**
   - Autenticación, seguridad, compliance, gobernanza de datos

4. **Innovación y Estrategia Técnica**
   - Nuevas tecnologías, optimización, investigación, estrategia

## Soft Skills Evaluadas

1. **Comunicación y Colaboración**
2. **Autonomía e Impacto**
3. **Adaptabilidad y Agilidad**
4. **Capacidad Analítica - Data-Centric Thinking**
5. **Comprensión de las necesidades del Cliente/Usuario**
6. **Empatía**
7. **Innovación**
8. **Pensamiento Crítico**
9. **Liderazgo y Mentoring** (desde Senior)
10. **Resolución de Conflictos** (para Staff Engineer y Tech Lead)

## Progresión de Habilidades por Nivel

### Habilidades Técnicas (escala 1-5)

| Nivel | DevOps & Entrega Continua | Diseño & Arquitectura | Seguridad & Datos | Innovación & Estrategia |
|-------|---------------------------|------------------------|-------------------|-------------------------|
| Junior | 1 | 1 | 1 | 1 |
| Semi Senior | 2 | 2 | 2 | 2 |
| Senior | 3 | 3 | 3 | 3 |
| Staff Engineer | 5 | 5 | 5 | 5 |
| Tech Lead | 4 | 4 | 4 | 4 |

### Soft Skills (escala 1-5)

| Nivel | Comunicación | Autonomía | Adaptabilidad | Data-Centric | User-Centric | Empatía | Innovación | Pensamiento Crítico | Liderazgo | Resolución Conflictos |
|-------|--------------|-----------|---------------|--------------|--------------|---------|------------|---------------------|-----------|------------------------|
| Junior | 1 | 1 | 1 | 1 | 1 | 1 | 1 | 1 | - | - |
| Semi Senior | 2 | 2 | 3 | 3 | 2 | 2 | 2 | 3 | - | - |
| Senior | 3 | 4 | 4 | 4 | 4 | 3 | 4 | 4 | 3 | - |
| Staff Engineer | 4 | 5 | 4 | 5 | 4 | 4 | 5 | 5 | 4 | 4 |
| Tech Lead | 4 | 4 | 4 | 4 | 5 | 5 | 4 | 4 | 5 | 5 |

## Responsabilidades Clave por Nivel

### Junior Developer
- Implementar features simples con supervisión
- Corregir bugs de complejidad baja a media
- Escribir pruebas unitarias
- Participar en code reviews para aprender
- Documentar su trabajo

### Semi Senior Developer
- Desarrollar features de complejidad media con mínima supervisión
- Resolver bugs de complejidad media a alta
- Escribir pruebas unitarias y de integración
- Realizar code reviews efectivos
- Contribuir a la documentación técnica
- Participar en planificación de sprints

### Senior Developer
- Diseñar e implementar features complejas de forma autónoma
- Liderar técnicamente proyectos de alcance medio
- Mentorizar a Junior y Semi Senior
- Participar en decisiones de arquitectura
- Identificar y abordar problemas sistémicos
- Evaluar y mitigar riesgos técnicos

### Staff Engineer
- Diseñar arquitectura de sistemas completos
- Definir estándares técnicos para toda la organización
- Liderar múltiples proyectos o iniciativas técnicas
- Evaluar y recomendar nuevas tecnologías
- Colaborar con liderazgo ejecutivo
- Resolver problemas técnicos de alta complejidad
- Mentorizar a Senior Engineers

### Tech Lead
- Liderazgo técnico de un equipo pequeño (3-5 personas)
- Contribución directa al código (40-60% del tiempo)
- Mentoring técnico continuo al equipo
- Planificación técnica de sprints y roadmap
- Facilitación de decisiones técnicas y arquitectónicas
- Colaboración estrecha con Product Managers
- Gestión de deuda técnica

## Comportamientos Esperados por Soft Skill

### 1. Comunicación y Colaboración
- **Junior**: Básica, principalmente escucha y aprende a comunicar problemas técnicos
- **Semi Senior**: Intercambia ideas con el equipo y explica decisiones técnicas
- **Senior**: Facilita discusiones técnicas y traduce conceptos complejos para audiencias no técnicas
- **Staff Engineer**: Comunica de forma clara y persuasiva en distintos niveles, facilita alineación técnica entre equipos
- **Tech Lead**: Coordina entre producto, diseño y desarrollo, establece expectativas claras

### 2. Autonomía e Impacto
- **Junior**: Completa tareas asignadas con supervisión alta o moderada
- **Semi Senior**: Maneja características completas con mínima supervisión
- **Senior**: Opera independientemente y genera impacto en múltiples áreas del proyecto
- **Staff Engineer**: Trabaja en problemas ambiguos con autonomía total, impacto en decisiones arquitecturales
- **Tech Lead**: Toma decisiones estratégicas de delivery técnico, balancea deuda técnica con velocidad

### 3. Adaptabilidad y Agilidad
- **Junior**: Se adapta a herramientas y metodologías del equipo
- **Semi Senior**: Se ajusta a cambios de prioridades y tecnologías con facilidad
- **Senior**: Guía cambios en procesos y adopción de nuevas tecnologías
- **Staff Engineer**: Promueve adaptabilidad técnica y cultural ante el crecimiento
- **Tech Lead**: Fomenta ciclos de iteración cortos y mejoras continuas

## Aplicación a Miembros del Equipo

Este plan de carrera servirá como base para crear planes de desarrollo personalizados para cada miembro del equipo, considerando:

1. Su nivel actual y próximo nivel objetivo
2. Sus fortalezas y áreas de desarrollo en habilidades técnicas y soft skills
3. Sus intereses y aspiraciones profesionales (track IC vs Management)
4. Los objetivos Q2 ya establecidos
5. La información recopilada en las reuniones 1:1

Los planes personalizados incluirán:
- Evaluación de nivel actual
- Objetivos de desarrollo específicos
- Acciones concretas para avanzar al siguiente nivel
- Métricas de éxito
- Cronograma de revisión de progreso
