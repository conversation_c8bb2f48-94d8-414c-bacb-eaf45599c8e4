# Plan de Desarrollo de Carrera - <PERSON>

## Estado actual
- **Rol actual**: Backend Developer (catalogado como Junior, evaluado como Semi Senior)
- **Asignación actual**: 100% equipo de backend (Santiago), ya no en IA/MLOps
- **Experiencia previa**: Cloud Engineer con experiencia en Python
- **Tiempo en la empresa**: Aproximadamente 1 año (desde junio 2024)
- **Formación académica**: Estudios incompletos de Física y Matemáticas universitarias
- **Estilo de aprendizaje**: Autodidacta en desarrollo de software
- **Intereses profesionales**: Diseño y arquitectura de sistemas, track de Individual Contributor
- **Aspiración a largo plazo**: Tener su propia empresa
- **Fecha de inicio del plan**: 20/03/2025 (primera reunión)
- **Fecha de evaluación de seniority**: 21/03/2025
- **Objetivo de progresión**: Senior en Q2 2025

## Objetivos de desarrollo

### <PERSON><PERSON><PERSON> plazo (3-6 meses)
- Progresión a Senior en Q2 2025
- "Pulir" temas técnicos identificados
- Desarrollar mayor seguridad al definir aspectos técnicos
- Abordar tendencia a la sobreingeniería
- Incrementar agilidad en desarrollo
- Desarrollar habilidades en diseño y arquitectura de sistemas

### Mediano plazo (6-12 meses)
- Consolidación como Senior Developer
- Profundización en diseño y arquitectura de sistemas
- Posible integración de IA en proyectos backend
- Desarrollo de proyectos que aprovechen su formación en Física/Matemáticas

### Largo plazo (12+ meses)
- Avance en el track de Individual Contributor
- Desarrollo de habilidades de liderazgo técnico sin gestión directa de personas
- Posible especialización en backend con IA
- Adquisición de habilidades empresariales (alineado con aspiración de tener empresa propia)

## Áreas de desarrollo

### Habilidades técnicas
- **Backend Development**: [Evaluado como nivel Semi Senior]
  - Áreas de mejora: Sobreingeniería, agilidad, seguridad en definiciones técnicas
  - Plan de acción: Identificar temas técnicos específicos a "pulir"

- **Diseño y Arquitectura de Sistemas**: [Área de interés para desarrollo]
  - Áreas de mejora: Desarrollar experiencia práctica
  - Plan de acción: Buscar oportunidades para participar en decisiones de diseño

- **Inteligencia Artificial / ML**: [Interés inicial, actualmente no aplicado]
  - Áreas de mejora: Integración práctica en proyectos actuales
  - Plan de acción: Explorar oportunidades para aplicar IA en contexto de backend

- **Cloud Engineering**: [Experiencia previa]
  - Áreas de mejora: Mantener conocimientos actualizados
  - Plan de acción: Identificar cómo aplicar esta experiencia en su rol actual

### Habilidades blandas
- **Comunicación**: [Auto-evaluado como buen nivel]
  - Áreas de mejora: Refinamiento continuo
  - Plan de acción: Buscar oportunidades para presentar ideas técnicas

- **Liderazgo técnico**: [Área para desarrollo]
  - Áreas de mejora: Influencia sin autoridad formal
  - Plan de acción: Oportunidades para liderar iniciativas técnicas

- **Ownership**: [Fortaleza identificada]
  - Áreas de mejora: Expandir a nivel de componentes o sistemas
  - Plan de acción: Asignar responsabilidad sobre áreas técnicas específicas

## Recursos y apoyo

### Capacitación
- **System Design & Arquitectura de Sistemas**: Capacitación interna durante Q2 2025 (junto con Tobias)
- Otros recursos: [Pendiente de identificar cursos/recursos específicos adicionales]

### Mentoring
- Técnico: Santiago (Tech Lead directo)
- Desarrollo profesional: Mauricio (Tech Lead)
- Peer learning: Colaboración con Tobias en capacitación de System Design

### Proyectos de desarrollo
- [Pendiente de identificar oportunidades específicas]

## Métricas de progreso
- **Progresión a Senior**: Evaluación en Q2 2025
- **Seguridad técnica**: Mejora en confianza al definir aspectos técnicos
- **Diseño y arquitectura**: Participación en decisiones de diseño
- **Sobreingeniería**: Reducción de complejidad innecesaria en soluciones
- **Agilidad**: Mejora en velocidad de entrega manteniendo calidad

## Revisiones de progreso
- Frecuencia: Mensual para seguimiento, trimestral para evaluación formal
- Próxima revisión: Abril 2025

## Notas y actualizaciones

### 24/04/2025 - Inicio de capacitación en System Design y Arquitectura
- Inicio formal del programa de capacitación en System Design y Arquitectura de Sistemas
- Programa estructurado de 24 semanas con entrega de material semanal
- Capacitación conjunta con Tobias Piraino
- Contenido alineado con interés en diseño y arquitectura de sistemas
- Plan completo documentado en `1on1_meetings/capacitacion/plan_system_design_arquitectura.md`

### 10/04/2025 - Seguimiento de capacitación
- Confirmado buen estado de salud y satisfacción con carga de trabajo
- Cumplimiento de entregas según lo esperado
- Compromiso del Tech Lead de enviar plan detallado de capacitación en System Design & Architecture
- Preparación para inicio de capacitación transversal con Tobias

### 21/03/2025 - Evaluación de seniority y decisión de capacitación
- Joel evaluado como Semi Senior con potencial para Senior en Q2 2025
- Identificado interés en track de Individual Contributor con enfoque en diseño y arquitectura
- Confirmada asignación 100% a equipo de backend (ya no en IA/MLOps)
- Identificada necesidad de "pulir" temas técnicos y desarrollar mayor seguridad
- Revelada aspiración a largo plazo de tener empresa propia
- Decisión de incorporar a Joel junto con Tobias en capacitación interna de System Design & Arquitectura de Sistemas durante Q2 2025

### 20/03/2025 - Creación inicial del plan
- Documento creado como estructura base para el plan de desarrollo
- Primera reunión formal con Joel y Santiago

---

*Este documento es dinámico y se actualizará regularmente a medida que Joel progrese en su desarrollo profesional.*
